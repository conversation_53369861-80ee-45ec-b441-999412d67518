<?php $__env->startSection('title', 'Subjects'); ?>
<?php $__env->startSection('page-title', 'Subject Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-book"></i>
                    All Subjects
                </h6>
                <a href="<?php echo e(route('admin.subjects.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i>
                    Add New Subject
                </a>
            </div>
            <div class="card-body">
                <?php if($subjects->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Questions</th>
                                    <th>Exams</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $subjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($subject->id); ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-success rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="bi bi-book text-white"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo e($subject->name); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if($subject->description): ?>
                                                <span class="text-muted"><?php echo e(Str::limit($subject->description, 50)); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">No description</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($subject->questions_count ?? 0); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo e($subject->exams_count ?? 0); ?></span>
                                        </td>
                                        <td>
                                            <small><?php echo e($subject->created_at->format('M d, Y')); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.subjects.show', $subject)); ?>" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.subjects.edit', $subject)); ?>" 
                                                   class="btn btn-sm btn-outline-warning" 
                                                   title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form method="POST" 
                                                      action="<?php echo e(route('admin.subjects.destroy', $subject)); ?>" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this subject?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($subjects->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-book text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No subjects found.</p>
                        <a href="<?php echo e(route('admin.subjects.create')); ?>" class="btn btn-primary">
                            <i class="bi bi-plus-lg"></i>
                            Add First Subject
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .avatar-sm {
        width: 2rem;
        height: 2rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp5\htdocs\eduexamin\codes\resources\views/admin/subjects/index.blade.php ENDPATH**/ ?>