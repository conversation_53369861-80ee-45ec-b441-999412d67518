@extends('admin.layouts.app')

@section('title', 'Exams')
@section('page-title', 'Exam Management')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clipboard-check"></i>
                    All Exams
                </h6>
                <a href="{{ route('admin.exams.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i>
                    Add New Exam
                </a>
            </div>
            <div class="card-body">
                @if($exams->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Title</th>
                                    <th>Subject</th>
                                    <th>Duration</th>
                                    <th>Questions</th>
                                    <th>Attempts</th>
                                    <th>Start Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($exams as $exam)
                                    <tr>
                                        <td>{{ $exam->id }}</td>
                                        <td>
                                            <div class="fw-bold">{{ $exam->title }}</div>
                                            @if($exam->description)
                                                <small class="text-muted">{{ Str::limit($exam->description, 50) }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $exam->subject->name }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">{{ $exam->duration }} min</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $exam->questions_count ?? 0 }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">{{ $exam->attempts_count ?? 0 }}</span>
                                        </td>
                                        <td>
                                            <small>{{ $exam->start_time->format('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            @php
                                                $now = now();
                                                $status = 'upcoming';
                                                $statusClass = 'secondary';
                                                
                                                if ($now->greaterThan($exam->end_time)) {
                                                    $status = 'ended';
                                                    $statusClass = 'danger';
                                                } elseif ($now->greaterThan($exam->start_time)) {
                                                    $status = 'active';
                                                    $statusClass = 'success';
                                                }
                                            @endphp
                                            <span class="badge bg-{{ $statusClass }}">{{ ucfirst($status) }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.exams.show', $exam) }}" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.exams.edit', $exam) }}" 
                                                   class="btn btn-sm btn-outline-warning" 
                                                   title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                @if($exam->attempts_count > 0)
                                                    <a href="{{ route('admin.exams.results', $exam) }}" 
                                                       class="btn btn-sm btn-outline-success" 
                                                       title="View Results">
                                                        <i class="bi bi-graph-up"></i>
                                                    </a>
                                                @endif
                                                <form method="POST" 
                                                      action="{{ route('admin.exams.destroy', $exam) }}" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this exam?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $exams->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-clipboard-check text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No exams found.</p>
                        <a href="{{ route('admin.exams.create') }}" class="btn btn-primary">
                            <i class="bi bi-plus-lg"></i>
                            Add First Exam
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
