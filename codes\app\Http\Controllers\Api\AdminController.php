<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Models\Student;
use App\Models\Exam;
use App\Models\Attempt;
use App\Models\Subject;
use App\Models\Question;
use Illuminate\Http\Request;


class AdminController extends Controller
{
    public function index()
    {
        return response()->json(Admin::paginate(15));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:admins',
            'password' => 'required|string|min:8',
        ]);

        $admin = Admin::create($request->all());
        $admin->assignRole('admin');

        return response()->json($admin, 201);
    }

    public function show(Admin $admin)
    {
        return response()->json($admin->load(['questions', 'exams']));
    }

    public function update(Request $request, Admin $admin)
    {
        $request->validate([
            'name' => 'string|max:255',
            'email' => 'string|email|max:255|unique:admins,email,' . $admin->id,
        ]);

        $admin->update($request->only(['name', 'email']));

        return response()->json($admin);
    }

    public function destroy(Admin $admin)
    {
        $admin->delete();
        return response()->json(null, 204);
    }

    public function dashboard()
    {
        return response()->json([
            'total_subjects' => Subject::count(),
            'total_questions' => Question::count(),
            'total_students' => Student::count(),
            'total_exams' => Exam::count(),
            'total_attempts' => Attempt::count(),
            'recent_attempts' => Attempt::with(['student', 'exam'])
                ->latest()
                ->take(10)
                ->get(),
        ]);
    }
}