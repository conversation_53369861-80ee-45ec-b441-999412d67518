<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Attempt;
use App\Models\Exam;
use App\Models\Subject;
use App\Models\Question;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Get student dashboard data
     */
    public function studentDashboard()
    {
        $student = auth()->user();
        
        // Get student's attempts statistics
        $totalAttempts = $student->attempts()->count();
        $completedAttempts = $student->attempts()->whereNotNull('submitted_at')->count();
        $examAttempts = $student->attempts()->where('type', 'exam')->count();
        $quizAttempts = $student->attempts()->where('type', 'quiz')->count();
        
        // Get scores statistics
        $completedScores = $student->attempts()
            ->whereNotNull('submitted_at')
            ->whereNotNull('score')
            ->pluck('score');
        
        $averageScore = $completedScores->count() > 0 ? round($completedScores->average(), 1) : 0;
        $highestScore = $completedScores->count() > 0 ? $completedScores->max() : 0;
        $lowestScore = $completedScores->count() > 0 ? $completedScores->min() : 0;
        
        // Get recent attempts (last 5)
        $recentAttempts = $student->attempts()
            ->with(['exam.subject', 'subject'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get()
            ->map(function ($attempt) {
                return [
                    'id' => $attempt->id,
                    'type' => $attempt->type,
                    'title' => $attempt->type === 'exam' 
                        ? $attempt->exam->title 
                        : $attempt->subject->name . ' Quiz',
                    'subject' => $attempt->type === 'exam' 
                        ? $attempt->exam->subject->name 
                        : $attempt->subject->name,
                    'score' => $attempt->score,
                    'status' => $attempt->submitted_at ? 'completed' : 'in_progress',
                    'started_at' => $attempt->started_at->format('M d, Y H:i'),
                    'submitted_at' => $attempt->submitted_at ? $attempt->submitted_at->format('M d, Y H:i') : null,
                ];
            });
        
        // Get available exams (upcoming and active)
        $now = Carbon::now();
        $availableExams = Exam::with('subject')
            ->where('start_time', '<=', $now)
            ->where('end_time', '>=', $now)
            ->whereDoesntHave('attempts', function ($query) use ($student) {
                $query->where('student_id', $student->id)
                      ->whereNotNull('submitted_at');
            })
            ->orderBy('end_time', 'asc')
            ->take(5)
            ->get()
            ->map(function ($exam) use ($now) {
                $timeLeft = $now->diffInMinutes($exam->end_time);
                return [
                    'id' => $exam->id,
                    'title' => $exam->title,
                    'subject' => $exam->subject->name,
                    'duration' => $exam->duration,
                    'questions_count' => $exam->questions()->count(),
                    'end_time' => $exam->end_time->format('M d, Y H:i'),
                    'time_left_minutes' => $timeLeft,
                    'time_left_formatted' => $this->formatTimeLeft($timeLeft),
                ];
            });
        
        // Get upcoming exams (not yet started)
        $upcomingExams = Exam::with('subject')
            ->where('start_time', '>', $now)
            ->orderBy('start_time', 'asc')
            ->take(5)
            ->get()
            ->map(function ($exam) use ($now) {
                $timeUntilStart = $now->diffInMinutes($exam->start_time);
                return [
                    'id' => $exam->id,
                    'title' => $exam->title,
                    'subject' => $exam->subject->name,
                    'duration' => $exam->duration,
                    'questions_count' => $exam->questions()->count(),
                    'start_time' => $exam->start_time->format('M d, Y H:i'),
                    'time_until_start_minutes' => $timeUntilStart,
                    'time_until_start_formatted' => $this->formatTimeLeft($timeUntilStart),
                ];
            });
        
        // Get saved exams count
        $savedExamsCount = $student->savedExams()->count();
        
        // Get subjects with question counts for quiz options
        $subjects = Subject::withCount('questions')
            ->having('questions_count', '>', 0)
            ->orderBy('name')
            ->get()
            ->map(function ($subject) {
                return [
                    'id' => $subject->id,
                    'name' => $subject->name,
                    'questions_count' => $subject->questions_count,
                ];
            });
        
        // Get performance by subject
        $subjectPerformance = $student->attempts()
            ->whereNotNull('submitted_at')
            ->whereNotNull('score')
            ->with(['exam.subject', 'subject'])
            ->get()
            ->groupBy(function ($attempt) {
                return $attempt->type === 'exam' 
                    ? $attempt->exam->subject->name 
                    : $attempt->subject->name;
            })
            ->map(function ($attempts, $subjectName) {
                $scores = $attempts->pluck('score');
                return [
                    'subject' => $subjectName,
                    'attempts_count' => $attempts->count(),
                    'average_score' => round($scores->average(), 1),
                    'highest_score' => $scores->max(),
                    'lowest_score' => $scores->min(),
                ];
            })
            ->values();
        
        return response()->json([
            'student' => [
                'id' => $student->id,
                'name' => $student->name,
                'email' => $student->email,
                'email_verified' => $student->email_verified_at !== null,
                'joined_date' => $student->created_at->format('M d, Y'),
            ],
            'statistics' => [
                'total_attempts' => $totalAttempts,
                'completed_attempts' => $completedAttempts,
                'exam_attempts' => $examAttempts,
                'quiz_attempts' => $quizAttempts,
                'average_score' => $averageScore,
                'highest_score' => $highestScore,
                'lowest_score' => $lowestScore,
                'saved_exams_count' => $savedExamsCount,
            ],
            'recent_attempts' => $recentAttempts,
            'available_exams' => $availableExams,
            'upcoming_exams' => $upcomingExams,
            'subjects' => $subjects,
            'subject_performance' => $subjectPerformance,
        ]);
    }
    
    /**
     * Get student's attempt history with pagination
     */
    public function attemptHistory(Request $request)
    {
        $student = auth()->user();
        
        $query = $student->attempts()->with(['exam.subject', 'subject']);
        
        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }
        
        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'completed') {
                $query->whereNotNull('submitted_at');
            } elseif ($request->status === 'in_progress') {
                $query->whereNull('submitted_at');
            }
        }
        
        // Filter by subject
        if ($request->filled('subject_id')) {
            $query->where(function ($q) use ($request) {
                $q->where('subject_id', $request->subject_id)
                  ->orWhereHas('exam', function ($examQuery) use ($request) {
                      $examQuery->where('subject_id', $request->subject_id);
                  });
            });
        }
        
        $attempts = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));
        
        $attempts->getCollection()->transform(function ($attempt) {
            return [
                'id' => $attempt->id,
                'type' => $attempt->type,
                'title' => $attempt->type === 'exam' 
                    ? $attempt->exam->title 
                    : $attempt->subject->name . ' Quiz',
                'subject' => $attempt->type === 'exam' 
                    ? $attempt->exam->subject->name 
                    : $attempt->subject->name,
                'score' => $attempt->score,
                'status' => $attempt->submitted_at ? 'completed' : 'in_progress',
                'started_at' => $attempt->started_at->format('M d, Y H:i'),
                'submitted_at' => $attempt->submitted_at ? $attempt->submitted_at->format('M d, Y H:i') : null,
                'duration_minutes' => $attempt->submitted_at 
                    ? $attempt->started_at->diffInMinutes($attempt->submitted_at)
                    : $attempt->started_at->diffInMinutes(now()),
            ];
        });
        
        return response()->json($attempts);
    }
    
    /**
     * Format time left in a human-readable format
     */
    private function formatTimeLeft($minutes)
    {
        if ($minutes < 60) {
            return $minutes . ' min';
        } elseif ($minutes < 1440) { // Less than 24 hours
            $hours = floor($minutes / 60);
            $remainingMinutes = $minutes % 60;
            return $hours . 'h ' . $remainingMinutes . 'm';
        } else {
            $days = floor($minutes / 1440);
            $remainingHours = floor(($minutes % 1440) / 60);
            return $days . 'd ' . $remainingHours . 'h';
        }
    }
}
