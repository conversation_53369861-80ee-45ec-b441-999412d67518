<?php



use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\AdminController;
use App\Http\Controllers\Api\StudentController;
use App\Http\Controllers\Api\SubjectController;
use App\Http\Controllers\Api\QuestionController;
use App\Http\Controllers\Api\ExamController;
use App\Http\Controllers\Api\AttemptController;

// Authentication routes
Route::prefix('auth')->group(function () {
    Route::post('admin/login', [AuthController::class, 'adminLogin']);
    Route::post('student/login', [AuthController::class, 'studentLogin']);
    Route::post('student/register', [AuthController::class, 'studentRegister']);
    Route::post('student/verify-code', [AuthController::class, 'verifyCode']);
    Route::post('logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');
    Route::get('me', [AuthController::class, 'me'])->middleware('auth:sanctum');
});

// Admin routes
Route::middleware(['auth:sanctum', 'role:admin'])->prefix('admin')->group(function () {
    Route::apiResource('admins', AdminController::class);
    Route::apiResource('students', StudentController::class);
    Route::apiResource('subjects', SubjectController::class);
    Route::apiResource('questions', QuestionController::class);
    Route::apiResource('exams', ExamController::class);

    // Exam management
    Route::post('exams/{exam}/questions', [ExamController::class, 'attachQuestions']);
    Route::delete('exams/{exam}/questions/{question}', [ExamController::class, 'detachQuestion']);

    // Analytics
    Route::get('analytics/dashboard', [AdminController::class, 'dashboard']);
    Route::get('analytics/exam/{exam}/results', [ExamController::class, 'results']);

    // Password update
    Route::put('password', [AuthController::class, 'updatePassword']);
});

// Student routes
Route::middleware(['auth:sanctum', 'role:student'])->prefix('student')->group(function () {
    Route::get('subjects', [SubjectController::class, 'index']);
    Route::get('exams/available', [ExamController::class, 'available']);
    Route::get('exams/icoming', [ExamController::class, 'incomingExams']);
    Route::get('exams/saved', [ExamController::class, 'savedExams']);
    Route::get('exams/{exam}', [ExamController::class, 'show']);

    // Attempts
    Route::post('exams/{exam}/attempt', [AttemptController::class, 'startExam']);
    Route::post('subjects/{subject}/quiz', [AttemptController::class, 'startQuiz']);
    Route::get('attempts/{attempt}', [AttemptController::class, 'show']);
    Route::post('attempts/{attempt}/submit', [AttemptController::class, 'submit']);
    Route::post('attempts/{attempt}/answer', [AttemptController::class, 'saveAnswer']);
    Route::get('my-attempts', [AttemptController::class, 'myAttempts']);

    // Save/Unsave exams
    Route::post('exams/{exam}/save', [ExamController::class, 'save']);
    Route::delete('exams/{exam}/unsave', [ExamController::class, 'unsave']);
    Route::get('password', [AuthController::class, 'updatePassword']);
});