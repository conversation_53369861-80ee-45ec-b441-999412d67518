<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;
use App\Models\VerificationCode;
use App\Mail\UserVerificationMail;
use Illuminate\Support\Facades\Mail;

class AuthController extends Controller
{
    public function adminLogin(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $admin = Admin::where('email', $request->email)->first();

        if (!$admin || !Hash::check($request->password, $admin->password)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        $admin->assignRole('admin');
        $token = $admin->createToken('admin-token')->plainTextToken;

        return response()->json([
            'user' => $admin,
            'token' => $token,
            'type' => 'admin'
        ]);
    }

     public function studentLogin(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $student = Student::where('email', $request->email)->first();

        if (!$student || !Hash::check($request->password, $student->password)) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        // Check if the student's email is verified
        if (!$student->email_verified_at) {
            // Optionally, you can resend the verification code here
            $this->sendVerificationCode($student);

            return response()->json([
                'verified' => false,
                'user' => $student,
                'type' => 'student',
                'message' => 'Your email is not verified. A new verification code has been sent.',
            ]);
        }

        // Issue token only if email is verified
        $token = $student->createToken('student-token')->plainTextToken;

        return response()->json([
            'verified' => true,
            'user' => $student,
            'token' => $token,
            'type' => 'student'
        ]);
    }



    public function studentRegister(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:students',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $student = Student::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);


        // 🔹 Send email verification code
        $this->sendVerificationCode($student);
        return response()->json([
            'user' => $student,
            'type' => 'student'
        ], 201);
    }

    public function verifyCode(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'code' => 'required|numeric',
        ]);

        $user = Student::where('email', $request->email)->first();

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $record = VerificationCode::where('student_id', $user->id)
            ->where('code', $request->code)
            ->latest()
            ->first();

        if (!$record || $record->isExpired()) {
            return response()->json(['message' => 'Invalid or expired code'], 400);
        }

        // Mark user as verified
        $user->update(['email_verified_at' => now()]);

        // Assign role only after verification
        $user->assignRole('student');

        // Delete used code
        $record->delete();
         
        // Issue token after verification
        $token = $user->createToken('student-token')->plainTextToken;

        return response()->json([
            'message' => 'Email verified successfully',
            'user' => $user,
            'token' => $token,
            'type' => 'student'
        ]);
    }



    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json(['message' => 'Logged out successfully']);
    }

    public function me(Request $request)
    {
        $user = $request->user();
        $type = $user instanceof Admin ? 'admin' : 'student';

        return response()->json([
            'user' => $user,
            'type' => $type,
            'roles' => $user->getRoleNames(),
            'permissions' => $user->getAllPermissions()->pluck('name')
        ]);
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = $request->user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json(['message' => 'Current password is incorrect'], 400);
        }

        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return response()->json(['message' => 'Password updated successfully']);
    }



    public function sendVerificationCode($user)
    {
        // Generate 6-digit random code
        $code = rand(100000, 999999);

        // Store in DB with expiration (30 mins from now)
        VerificationCode::create([
            'student_id' => $user->id,
            'code' => $code,
            'expires_at' => Carbon::now()->addMinutes(30),
        ]);

        // Send via email
        Mail::to($user->email)->queue(new UserVerificationMail($user, $code));
    }
}
