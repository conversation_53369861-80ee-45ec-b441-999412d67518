@extends('admin.layouts.app')

@section('title', 'Student Attempts')
@section('page-title', 'Student Attempts')

@section('content')
<div class="row">
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-circle"></i>
                    Student Information
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="avatar-lg bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                    <i class="bi bi-person text-white" style="font-size: 2rem;"></i>
                </div>
                <h5 class="card-title">{{ $student->name }}</h5>
                <p class="text-muted">{{ $student->email }}</p>
                
                @if($student->email_verified_at)
                    <span class="badge bg-success mb-3">Email Verified</span>
                @else
                    <span class="badge bg-warning mb-3">Email Not Verified</span>
                @endif

                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="h4 text-primary">{{ $statistics['total_attempts'] }}</div>
                        <small class="text-muted">Total Attempts</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success">{{ $statistics['completed_attempts'] }}</div>
                        <small class="text-muted">Completed</small>
                    </div>
                </div>

                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="h5 text-info">{{ $statistics['exam_attempts'] }}</div>
                        <small class="text-muted">Exams</small>
                    </div>
                    <div class="col-6">
                        <div class="h5 text-warning">{{ $statistics['quiz_attempts'] }}</div>
                        <small class="text-muted">Quizzes</small>
                    </div>
                </div>

                @if($statistics['completed_attempts'] > 0)
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="h6 text-success">{{ $statistics['average_score'] }}%</div>
                            <small class="text-muted">Average Score</small>
                        </div>
                        <div class="col-6">
                            <div class="h6 text-primary">{{ $statistics['highest_score'] }}%</div>
                            <small class="text-muted">Highest Score</small>
                        </div>
                    </div>
                @endif

                <hr>

                <div class="text-muted">
                    <small>
                        <i class="bi bi-calendar"></i>
                        Joined {{ $student->created_at->format('M d, Y') }}
                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.students.index') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Students
                    </a>
                    <a href="{{ route('admin.students.show', $student) }}" class="btn btn-primary">
                        <i class="bi bi-eye"></i>
                        View Profile
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clipboard-data"></i>
                    Attempt History ({{ $attempts->total() }})
                </h6>
            </div>
            <div class="card-body">
                @if($attempts->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Type</th>
                                    <th>Exam/Subject</th>
                                    <th>Score</th>
                                    <th>Started</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($attempts as $attempt)
                                    <tr>
                                        <td>{{ $attempt->id }}</td>
                                        <td>
                                            <span class="badge bg-{{ $attempt->type === 'exam' ? 'primary' : 'info' }}">
                                                {{ ucfirst($attempt->type) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($attempt->type === 'exam' && $attempt->exam)
                                                <div class="fw-bold">{{ $attempt->exam->title }}</div>
                                                <small class="text-muted">{{ $attempt->exam->subject->name }}</small>
                                            @elseif($attempt->type === 'quiz' && $attempt->subject)
                                                <div class="fw-bold">{{ $attempt->subject->name }}</div>
                                                <small class="text-muted">Quiz</small>
                                            @else
                                                <span class="text-muted">N/A</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attempt->score !== null)
                                                <span class="fw-bold text-{{ $attempt->score >= 60 ? 'success' : 'danger' }}">
                                                    {{ $attempt->score }}%
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $attempt->started_at->format('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                @php
                                                    $duration = $attempt->started_at->diffInMinutes($attempt->submitted_at);
                                                @endphp
                                                <small>{{ $duration }} min</small>
                                            @else
                                                @php
                                                    $duration = $attempt->started_at->diffInMinutes(now());
                                                @endphp
                                                <small class="text-warning">{{ $duration }} min (ongoing)</small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                <span class="badge bg-success">Completed</span>
                                            @else
                                                <span class="badge bg-warning">In Progress</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.attempts.show', $attempt) }}" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <form method="POST" 
                                                      action="{{ route('admin.attempts.destroy', $attempt) }}" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this attempt?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $attempts->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-clipboard-data text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No attempts found for this student.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-lg {
        width: 4rem;
        height: 4rem;
    }
</style>
@endpush
