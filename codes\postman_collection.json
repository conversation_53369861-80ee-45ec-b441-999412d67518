{"info": {"name": "Exam System API", "description": "Complete API collection for the Laravel Exam System with Admin and Student endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}, {"key": "student_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Student Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Student User\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/student/register", "host": ["{{base_url}}"], "path": ["auth", "student", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('student_token', response.token);", "    pm.test('Student registered successfully', () => {", "        pm.expect(response.type).to.equal('student');", "        pm.expect(response.token).to.not.be.empty;", "    });", "}"]}}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/admin/login", "host": ["{{base_url}}"], "path": ["auth", "admin", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('admin_token', response.token);", "}"]}}]}, {"name": "Student Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/student/login", "host": ["{{base_url}}"], "path": ["auth", "student", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('student_token', response.token);", "}"]}}]}, {"name": "Get Current User (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/auth/me", "host": ["{{base_url}}"], "path": ["auth", "me"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}}, {"name": "Admin Update Password", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"current_password\": \"password123\",\n    \"password\": \"newpassword123\",\n    \"password_confirmation\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/admin/password", "host": ["{{base_url}}"], "path": ["admin", "password"]}}}, {"name": "Student Update Password", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"current_password\": \"password123\",\n    \"password\": \"newpassword123\",\n    \"password_confirmation\": \"newpassword123\"\n}"}, "url": {"raw": "{{base_url}}/student/password", "host": ["{{base_url}}"], "path": ["student", "password"]}}}]}, {"name": "Admin - Subjects", "item": [{"name": "Create Subject", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Mathematics\",\n    \"description\": \"Basic mathematics subject covering algebra and geometry\"\n}"}, "url": {"raw": "{{base_url}}/admin/subjects", "host": ["{{base_url}}"], "path": ["admin", "subjects"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('subject_id', response.id);", "}"]}}]}, {"name": "Get All Subjects", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/subjects", "host": ["{{base_url}}"], "path": ["admin", "subjects"]}}}, {"name": "Get Subject by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/subjects/{{subject_id}}", "host": ["{{base_url}}"], "path": ["admin", "subjects", "{{subject_id}}"]}}}, {"name": "Update Subject", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Advanced Mathematics\",\n    \"description\": \"Advanced mathematics covering calculus and statistics\"\n}"}, "url": {"raw": "{{base_url}}/admin/subjects/{{subject_id}}", "host": ["{{base_url}}"], "path": ["admin", "subjects", "{{subject_id}}"]}}}]}, {"name": "Admin - Questions", "item": [{"name": "Create MCQ Question", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"subject_id\": {{subject_id}},\n    \"question_text\": \"What is 2 + 2?\",\n    \"direction\": \"ltr\",\n    \"type\": \"mcq\",\n    \"options\": [\n        {\n            \"option_text\": \"3\",\n            \"direction\": \"ltr\",\n            \"is_correct\": false\n        },\n        {\n            \"option_text\": \"4\",\n            \"direction\": \"ltr\",\n            \"is_correct\": true\n        },\n        {\n            \"option_text\": \"5\",\n            \"direction\": \"ltr\",\n            \"is_correct\": false\n        },\n        {\n            \"option_text\": \"6\",\n            \"direction\": \"ltr\",\n            \"is_correct\": false\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/admin/questions", "host": ["{{base_url}}"], "path": ["admin", "questions"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('question_id', response.id);", "}"]}}]}, {"name": "Create True/False Question", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"subject_id\": {{subject_id}},\n    \"question_text\": \"The square root of 16 is 4.\",\n    \"direction\": \"ltr\",\n    \"type\": \"true_false\",\n    \"options\": [\n        {\n            \"option_text\": \"True\",\n            \"direction\": \"ltr\",\n            \"is_correct\": true\n        },\n        {\n            \"option_text\": \"False\",\n            \"direction\": \"ltr\",\n            \"is_correct\": false\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/admin/questions", "host": ["{{base_url}}"], "path": ["admin", "questions"]}}}, {"name": "Get All Questions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/questions", "host": ["{{base_url}}"], "path": ["admin", "questions"]}}}, {"name": "Get Questions by Subject", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/questions?subject_id={{subject_id}}", "host": ["{{base_url}}"], "path": ["admin", "questions"], "query": [{"key": "subject_id", "value": "{{subject_id}}"}]}}}, {"name": "Get Question by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/questions/{{question_id}}", "host": ["{{base_url}}"], "path": ["admin", "questions", "{{question_id}}"]}}}]}, {"name": "Admin - <PERSON><PERSON>", "item": [{"name": "Create Exam", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"subject_id\": {{subject_id}},\n    \"title\": \"Mathematics Midterm Exam\",\n    \"description\": \"Midterm examination covering basic mathematics\",\n    \"start_time\": \"2024-12-20T09:00:00Z\",\n    \"end_time\": \"2024-12-20T12:00:00Z\",\n    \"duration\": 120,\n    \"question_ids\": [{{question_id}}]\n}"}, "url": {"raw": "{{base_url}}/admin/exams", "host": ["{{base_url}}"], "path": ["admin", "exams"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('exam_id', response.id);", "}"]}}]}, {"name": "Get All Exams", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/exams", "host": ["{{base_url}}"], "path": ["admin", "exams"]}}}, {"name": "Get Exam by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/exams/{{exam_id}}", "host": ["{{base_url}}"], "path": ["admin", "exams", "{{exam_id}}"]}}}, {"name": "Attach Questions to <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"question_ids\": [{{question_id}}]\n}"}, "url": {"raw": "{{base_url}}/admin/exams/{{exam_id}}/questions", "host": ["{{base_url}}"], "path": ["admin", "exams", "{{exam_id}}", "questions"]}}}, {"name": "Get Exam Results", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/analytics/exam/{{exam_id}}/results", "host": ["{{base_url}}"], "path": ["admin", "analytics", "exam", "{{exam_id}}", "results"]}}}]}, {"name": "Admin - Analytics", "item": [{"name": "Dashboard Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/analytics/dashboard", "host": ["{{base_url}}"], "path": ["admin", "analytics", "dashboard"]}}}]}, {"name": "Admin - Students Management", "item": [{"name": "Get All Students", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/admin/students", "host": ["{{base_url}}"], "path": ["admin", "students"]}}}, {"name": "Create Student", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/admin/students", "host": ["{{base_url}}"], "path": ["admin", "students"]}}}]}, {"name": "Student - Exams", "item": [{"name": "Get Available Exams", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/student/exams/available", "host": ["{{base_url}}"], "path": ["student", "exams", "available"]}}}, {"name": "Get Exam <PERSON>", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/student/exams/{{exam_id}}", "host": ["{{base_url}}"], "path": ["student", "exams", "{{exam_id}}"]}}}, {"name": "Start Exam <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/student/exams/{{exam_id}}/attempt", "host": ["{{base_url}}"], "path": ["student", "exams", "{{exam_id}}", "attempt"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('attempt_id', response.id);", "    if (response.attemptQuestions && response.attemptQuestions.length > 0) {", "        pm.collectionVariables.set('attempt_question_id', response.attemptQuestions[0].id);", "        if (response.attemptQuestions[0].attemptOptions && response.attemptQuestions[0].attemptOptions.length > 0) {", "            pm.collectionVariables.set('attempt_option_id', response.attemptQuestions[0].attemptOptions[0].id);", "        }", "    }", "}"]}}]}]}, {"name": "Student - Quiz", "item": [{"name": "Get Subjects", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/student/subjects", "host": ["{{base_url}}"], "path": ["student", "subjects"]}}}, {"name": "Start Quiz Attempt", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"question_count\": 5\n}"}, "url": {"raw": "{{base_url}}/student/subjects/{{subject_id}}/quiz", "host": ["{{base_url}}"], "path": ["student", "subjects", "{{subject_id}}", "quiz"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('quiz_attempt_id', response.id);", "    if (response.attemptQuestions && response.attemptQuestions.length > 0) {", "        pm.collectionVariables.set('quiz_attempt_question_id', response.attemptQuestions[0].id);", "        if (response.attemptQuestions[0].attemptOptions && response.attemptQuestions[0].attemptOptions.length > 0) {", "            pm.collectionVariables.set('quiz_attempt_option_id', response.attemptQuestions[0].attemptOptions[0].id);", "        }", "    }", "}"]}}]}]}, {"name": "Student - Attempts", "item": [{"name": "Get Attempt Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/student/attempts/{{attempt_id}}", "host": ["{{base_url}}"], "path": ["student", "attempts", "{{attempt_id}}"]}}}, {"name": "Save Answer", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"attempt_question_id\": {{attempt_question_id}},\n    \"selected_option_ids\": [{{attempt_option_id}}]\n}"}, "url": {"raw": "{{base_url}}/student/attempts/{{attempt_id}}/answer", "host": ["{{base_url}}"], "path": ["student", "attempts", "{{attempt_id}}", "answer"]}}}, {"name": "Submit Attempt", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/student/attempts/{{attempt_id}}/submit", "host": ["{{base_url}}"], "path": ["student", "attempts", "{{attempt_id}}", "submit"]}}}, {"name": "Get My Attempts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/student/my-attempts", "host": ["{{base_url}}"], "path": ["student", "my-attempts"]}}}]}, {"name": "<PERSON> <PERSON><PERSON> (Student)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/student/exams/:id/save", "host": ["{{base_url}}"], "path": ["student", "exams", ":id", "save"]}}}, {"name": "<PERSON><PERSON><PERSON> (Student)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{student_token}}"}, {"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/student/exams/:id/unsave", "host": ["{{base_url}}"], "path": ["student", "exams", ":id", "unsave"]}}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Response time is less than 2000ms', () => {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "pm.test('Response has correct content type', () => {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/json');", "});"]}}]}