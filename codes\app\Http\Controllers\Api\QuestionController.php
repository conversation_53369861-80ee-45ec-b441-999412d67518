<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Question;
use Illuminate\Http\Request;

class QuestionController extends Controller
{
    public function index(Request $request)
    {
        $query = Question::with(['subject', 'options', 'admin']);

        if ($request->subject_id) {
            $query->where('subject_id', $request->subject_id);
        }

        if ($request->type) {
            $query->where('type', $request->type);
        }

        return response()->json($query->paginate(15));
    }

    public function store(Request $request)
    {
        $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'question_text' => 'required|string',
            'direction' => 'in:ltr,rtl',
            'type' => 'required|in:mcq,true_false',
            'options' => 'required|array|min:2',
            'options.*.option_text' => 'required|string',
            'options.*.direction' => 'in:ltr,rtl',
            'options.*.is_correct' => 'required|boolean',
        ]);

        $question = Question::create([
            'subject_id' => $request->subject_id,
            'question_text' => $request->question_text,
            'direction' => $request->direction ?? 'ltr',
            'type' => $request->type,
            'created_by' => auth()->id(),
        ]);

        foreach ($request->options as $optionData) {
            $question->options()->create([
                'option_text' => $optionData['option_text'],
                'direction' => $optionData['direction'] ?? 'ltr',
                'is_correct' => $optionData['is_correct'],
            ]);
        }

        return response()->json($question->load('options'), 201);
    }

    public function show(Question $question)
    {
        return response()->json($question->load(['subject', 'options', 'admin']));
    }

    public function update(Request $request, Question $question)
    {
        $request->validate([
            'subject_id' => 'exists:subjects,id',
            'question_text' => 'string',
            'direction' => 'in:ltr,rtl',
            'type' => 'in:mcq,true_false',
            'options' => 'array|min:2',
            'options.*.option_text' => 'required_with:options|string',
            'options.*.direction' => 'in:ltr,rtl',
            'options.*.is_correct' => 'required_with:options|boolean',
        ]);

        $question->update($request->only(['subject_id', 'question_text', 'direction', 'type']));

        if ($request->has('options')) {
            $question->options()->delete();
            foreach ($request->options as $optionData) {
                $question->options()->create([
                    'option_text' => $optionData['option_text'],
                    'direction' => $optionData['direction'] ?? 'ltr',
                    'is_correct' => $optionData['is_correct'],
                ]);
            }
        }

        return response()->json($question->load('options'));
    }

    public function destroy(Question $question)
    {
        $question->delete();
        return response()->json(null, 204);
    }
}