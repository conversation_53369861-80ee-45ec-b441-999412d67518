<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AttemptOption extends Model
{
    use HasFactory;

    protected $fillable = [
        'attempt_question_id',
        'option_id',
        'option_text',
        'direction',
        'is_correct',
        'selected',
    ];

    protected function casts(): array
    {
        return [
            'is_correct' => 'boolean',
            'selected' => 'boolean',
        ];
    }

    public function attemptQuestion()
    {
        return $this->belongsTo(AttemptQuestion::class);
    }

    public function option()
    {
        return $this->belongsTo(Option::class);
    }
}