<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\AuthController as AdminAuthController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\StudentController;
use App\Http\Controllers\Admin\SubjectController;
use App\Http\Controllers\Admin\QuestionController;
use App\Http\Controllers\Admin\ExamController;
use App\Http\Controllers\Admin\AttemptController;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/login', function () {
    return redirect()->route('admin.login');
})->name('login');


// Admin Authentication Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Guest routes (not authenticated)
    Route::middleware('guest:admin')->group(function () {
        Route::get('login', [AdminAuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [AdminAuthController::class, 'login']);
    });

    // Authenticated admin routes
    Route::middleware('auth:admin')->group(function () {
        Route::post('logout', [AdminAuthController::class, 'logout'])->name('logout');
        Route::get('password', [AdminAuthController::class, 'showPasswordForm'])->name('password.form');
        Route::put('password', [AdminAuthController::class, 'updatePassword'])->name('password.update');

        // Dashboard
        Route::get('dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/', [AdminController::class, 'dashboard']); // Redirect admin root to dashboard

        // Admin Management
        Route::resource('admins', AdminController::class);
        Route::get('admins/{admin}/questions', [AdminController::class, 'questions'])
            ->name('admins.questions');

        // Student Management
        Route::resource('students', StudentController::class);


        // Subject Management
        Route::resource('subjects', SubjectController::class);
        Route::get('subjects/{subject}/questions', [SubjectController::class, 'questions'])
            ->name('subjects.questions');

        // Question Management
        Route::resource('questions', QuestionController::class);

        // Exam Management
        Route::resource('exams', ExamController::class);
        Route::get('exams/{exam}/questions/attach', [ExamController::class, 'attachQuestionsForm'])->name('exams.attach-questions-form');
        Route::post('exams/{exam}/questions', [ExamController::class, 'attachQuestions'])->name('exams.attach-questions');
        Route::delete('exams/{exam}/questions/{question}', [ExamController::class, 'detachQuestion'])->name('exams.detach-question');
        Route::get('exams/{exam}/results', [ExamController::class, 'results'])->name('exams.results');
        Route::get('exams/{exam}/attempts', [AttemptController::class, 'examAttempts'])->name('exams.attempts');

        // Attempt Management
        Route::resource('attempts', AttemptController::class)->only(['index', 'show', 'destroy']);
        Route::get('students/{student}/attempts', [AttemptController::class, 'studentAttempts'])->name('students.attempts');
    });
});

use Illuminate\Support\Facades\Mail;

Route::get('/test-mail', function () {
    Mail::raw('Hello Ali ibrahim!', function ($message) {
        $message->to('<EMAIL>')
            ->subject('Test Email');
    });

    return 'Email sent successfully!';
});
