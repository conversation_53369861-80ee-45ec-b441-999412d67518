<?php $__env->startSection('title', 'Attempt Details'); ?>
<?php $__env->startSection('page-title', 'Attempt Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    Attempt Information
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg bg-<?php echo e($attempt->type === 'exam' ? 'primary' : 'info'); ?> rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                        <i class="bi bi-<?php echo e($attempt->type === 'exam' ? 'clipboard-check' : 'question-circle'); ?> text-white" style="font-size: 2rem;"></i>
                    </div>
                    <h6 class="card-title"><?php echo e(ucfirst($attempt->type)); ?> Attempt #<?php echo e($attempt->id); ?></h6>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Student</label>
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                            <i class="bi bi-person text-white"></i>
                        </div>
                        <div>
                            <div class="fw-bold"><?php echo e($attempt->student->name); ?></div>
                            <small class="text-muted"><?php echo e($attempt->student->email); ?></small>
                        </div>
                    </div>
                </div>

                <?php if($attempt->type === 'exam' && $attempt->exam): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted">Exam</label>
                        <div>
                            <div class="fw-bold"><?php echo e($attempt->exam->title); ?></div>
                            <small class="text-muted"><?php echo e($attempt->exam->subject->name); ?></small>
                        </div>
                    </div>
                <?php elseif($attempt->type === 'quiz' && $attempt->subject): ?>
                    <div class="mb-3">
                        <label class="form-label text-muted">Subject</label>
                        <div>
                            <div class="fw-bold"><?php echo e($attempt->subject->name); ?></div>
                            <small class="text-muted">Quiz</small>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="mb-3">
                    <label class="form-label text-muted">Score</label>
                    <div>
                        <?php if($attempt->score !== null): ?>
                            <span class="badge bg-<?php echo e($attempt->score >= 60 ? 'success' : 'danger'); ?> fs-6">
                                <?php echo e($attempt->score); ?>%
                            </span>
                        <?php else: ?>
                            <span class="badge bg-secondary fs-6">Not Completed</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Status</label>
                    <div>
                        <?php if($attempt->submitted_at): ?>
                            <span class="badge bg-success fs-6">Completed</span>
                        <?php else: ?>
                            <span class="badge bg-warning fs-6">In Progress</span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Timeline</label>
                    <div>
                        <small class="text-muted">
                            <i class="bi bi-play-circle"></i>
                            Started: <?php echo e($attempt->started_at->format('M d, Y H:i')); ?>

                        </small><br>
                        <?php if($attempt->submitted_at): ?>
                            <small class="text-muted">
                                <i class="bi bi-stop-circle"></i>
                                Completed: <?php echo e($attempt->submitted_at->format('M d, Y H:i')); ?>

                            </small><br>
                            <small class="text-success">
                                <i class="bi bi-clock"></i>
                                Duration: <?php echo e($attempt->started_at->diffInMinutes($attempt->submitted_at)); ?> minutes
                            </small>
                        <?php else: ?>
                            <small class="text-warning">
                                <i class="bi bi-clock"></i>
                                Ongoing: <?php echo e($attempt->started_at->diffInMinutes(now())); ?> minutes
                            </small>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="row text-center mb-3">
                    <div class="col-4">
                        <div class="h5 text-primary"><?php echo e($statistics['total_questions']); ?></div>
                        <small class="text-muted">Total</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 text-success"><?php echo e($statistics['correct_answers']); ?></div>
                        <small class="text-muted">Correct</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 text-danger"><?php echo e($statistics['incorrect_answers']); ?></div>
                        <small class="text-muted">Incorrect</small>
                    </div>
                </div>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="h6 text-info"><?php echo e($statistics['answered_questions']); ?></div>
                        <small class="text-muted">Answered</small>
                    </div>
                    <div class="col-6">
                        <div class="h6 text-warning"><?php echo e($statistics['unanswered_questions']); ?></div>
                        <small class="text-muted">Unanswered</small>
                    </div>
                </div>

                <?php if($statistics['answered_questions'] > 0): ?>
                    <div class="mt-3">
                        <label class="form-label text-muted">Accuracy</label>
                        <div class="progress">
                            <div class="progress-bar bg-<?php echo e($statistics['accuracy'] >= 60 ? 'success' : 'danger'); ?>" 
                                 role="progressbar" 
                                 style="width: <?php echo e($statistics['accuracy']); ?>%">
                                <?php echo e($statistics['accuracy']); ?>%
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="<?php echo e(route('admin.attempts.index')); ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to List
                    </a>
                    <a href="<?php echo e(route('admin.students.attempts', $attempt->student)); ?>" class="btn btn-outline-primary">
                        <i class="bi bi-person"></i>
                        Student Attempts
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Questions and Answers -->
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-check"></i>
                    Questions & Answers (<?php echo e($attempt->attemptQuestions->count()); ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if($attempt->attemptQuestions->count() > 0): ?>
                    <?php $__currentLoopData = $attempt->attemptQuestions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $attemptQuestion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="card mb-3 border-<?php echo e($attemptQuestion->attemptOptions->where('selected', true)->where('is_correct', true)->count() > 0 && $attemptQuestion->attemptOptions->where('selected', true)->where('is_correct', false)->count() === 0 ? 'success' : ($attemptQuestion->attemptOptions->where('selected', true)->count() > 0 ? 'danger' : 'warning')); ?>">
                            <div class="card-header py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Question <?php echo e($index + 1); ?></h6>
                                    <?php
                                        $selectedOptions = $attemptQuestion->attemptOptions->where('selected', true);
                                        $correctSelected = $selectedOptions->where('is_correct', true)->count();
                                        $incorrectSelected = $selectedOptions->where('is_correct', false)->count();
                                        $totalCorrect = $attemptQuestion->attemptOptions->where('is_correct', true)->count();
                                        
                                        $isCorrect = $correctSelected === $totalCorrect && $incorrectSelected === 0 && $correctSelected > 0;
                                        $isAnswered = $selectedOptions->count() > 0;
                                    ?>
                                    
                                    <?php if($isCorrect): ?>
                                        <span class="badge bg-success">Correct</span>
                                    <?php elseif($isAnswered): ?>
                                        <span class="badge bg-danger">Incorrect</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Unanswered</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="p-2 bg-light rounded" dir="<?php echo e($attemptQuestion->direction); ?>">
                                        <strong><?php echo e($attemptQuestion->question_text); ?></strong>
                                    </div>
                                    <?php if($attemptQuestion->direction === 'rtl'): ?>
                                        <small class="text-muted">RTL</small>
                                    <?php endif; ?>
                                </div>

                                <div class="row">
                                    <?php $__currentLoopData = $attemptQuestion->attemptOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="p-2 rounded border 
                                                <?php echo e($option->selected && $option->is_correct ? 'bg-success bg-opacity-10 border-success' : ''); ?>

                                                <?php echo e($option->selected && !$option->is_correct ? 'bg-danger bg-opacity-10 border-danger' : ''); ?>

                                                <?php echo e(!$option->selected && $option->is_correct ? 'bg-warning bg-opacity-10 border-warning' : ''); ?>

                                                <?php echo e(!$option->selected && !$option->is_correct ? 'border-light' : ''); ?>">
                                                
                                                <div class="d-flex align-items-center">
                                                    <?php if($option->selected): ?>
                                                        <i class="bi bi-check-circle-fill text-<?php echo e($option->is_correct ? 'success' : 'danger'); ?> me-2"></i>
                                                    <?php elseif($option->is_correct): ?>
                                                        <i class="bi bi-exclamation-circle-fill text-warning me-2"></i>
                                                    <?php else: ?>
                                                        <i class="bi bi-circle me-2 text-muted"></i>
                                                    <?php endif; ?>
                                                    
                                                    <div dir="<?php echo e($option->direction); ?>">
                                                        <?php echo e($option->option_text); ?>

                                                    </div>
                                                </div>
                                                
                                                <div class="mt-1">
                                                    <?php if($option->selected): ?>
                                                        <small class="badge bg-<?php echo e($option->is_correct ? 'success' : 'danger'); ?>">
                                                            Selected <?php echo e($option->is_correct ? '(Correct)' : '(Incorrect)'); ?>

                                                        </small>
                                                    <?php elseif($option->is_correct): ?>
                                                        <small class="badge bg-warning">Correct Answer</small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-question-circle text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No questions found for this attempt.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .avatar-lg {
        width: 4rem;
        height: 4rem;
    }
    .avatar-sm {
        width: 1.5rem;
        height: 1.5rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp5\htdocs\eduexamin\codes\resources\views/admin/attempts/show.blade.php ENDPATH**/ ?>