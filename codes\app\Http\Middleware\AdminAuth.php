<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AdminAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::guard('admin')->check()) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Unauthenticated.'], 401);
            }
            
            return redirect()->route('admin.login')
                ->with('error', 'Please login to access the admin panel.');
        }

        // Ensure the authenticated admin has the admin role
        $admin = Auth::guard('admin')->user();
        if (!$admin->hasRole('admin')) {
            $admin->assignRole('admin');
        }

        return $next($request);
    }
}
