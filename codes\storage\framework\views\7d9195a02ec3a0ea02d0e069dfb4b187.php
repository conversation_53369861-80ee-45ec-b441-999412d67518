<?php $__env->startSection('title', 'Create Question'); ?>
<?php $__env->startSection('page-title', 'Create New Question'); ?>

<?php $__env->startSection('content'); ?>
<div class="row justify-content-center">
    <div class="col-md-10 col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-question-circle-fill"></i>
                    Create New Question
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?php echo e(route('admin.questions.store')); ?>" id="questionForm">
                    <?php echo csrf_field(); ?>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subject_id" class="form-label">Subject</label>
                                <select class="form-select <?php $__errorArgs = ['subject_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="subject_id" 
                                        name="subject_id" 
                                        required>
                                    <option value="">Select Subject</option>
                                    <?php $__currentLoopData = $subjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($subject->id); ?>" 
                                                <?php echo e(old('subject_id', request('subject_id')) == $subject->id ? 'selected' : ''); ?>>
                                            <?php echo e($subject->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['subject_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">Question Type</label>
                                <select class="form-select <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="type" 
                                        name="type" 
                                        required>
                                    <option value="">Select Type</option>
                                    <option value="mcq" <?php echo e(old('type') == 'mcq' ? 'selected' : ''); ?>>Multiple Choice</option>
                                    <option value="true_false" <?php echo e(old('type') == 'true_false' ? 'selected' : ''); ?>>True/False</option>
                                </select>
                                <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="question_text" class="form-label">Question Text</label>
                        <textarea class="form-control <?php $__errorArgs = ['question_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="question_text" 
                                  name="question_text" 
                                  rows="3" 
                                  required
                                  placeholder="Enter the question text"><?php echo e(old('question_text')); ?></textarea>
                        <?php $__errorArgs = ['question_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="mb-3">
                        <label for="direction" class="form-label">Text Direction</label>
                        <select class="form-select <?php $__errorArgs = ['direction'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                id="direction" 
                                name="direction" 
                                required>
                            <option value="ltr" <?php echo e(old('direction', 'ltr') == 'ltr' ? 'selected' : ''); ?>>Left to Right (LTR)</option>
                            <option value="rtl" <?php echo e(old('direction') == 'rtl' ? 'selected' : ''); ?>>Right to Left (RTL)</option>
                        </select>
                        <?php $__errorArgs = ['direction'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <hr>
                    <h6 class="text-primary">Answer Options</h6>
                    <p class="text-muted small">Add at least 2 options and mark the correct answer(s).</p>

                    <div id="options-container">
                        <?php
                            $oldOptions = old('options', [
                                ['option_text' => '', 'direction' => 'ltr', 'is_correct' => '0'],
                                ['option_text' => '', 'direction' => 'ltr', 'is_correct' => '0']
                            ]);
                        ?>
                        
                        <?php $__currentLoopData = $oldOptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="option-item mb-3 p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">Option <?php echo e($index + 1); ?></h6>
                                    <?php if($index > 1): ?>
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-option">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-8">
                                        <input type="text" 
                                               class="form-control <?php $__errorArgs = ['options.'.$index.'.option_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               name="options[<?php echo e($index); ?>][option_text]" 
                                               value="<?php echo e($option['option_text']); ?>"
                                               placeholder="Enter option text" 
                                               required>
                                        <?php $__errorArgs = ['options.'.$index.'.option_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select" name="options[<?php echo e($index); ?>][direction]">
                                            <option value="ltr" <?php echo e($option['direction'] == 'ltr' ? 'selected' : ''); ?>>LTR</option>
                                            <option value="rtl" <?php echo e($option['direction'] == 'rtl' ? 'selected' : ''); ?>>RTL</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   name="options[<?php echo e($index); ?>][is_correct]" 
                                                   value="1"
                                                   <?php echo e($option['is_correct'] == '1' ? 'checked' : ''); ?>>
                                            <label class="form-check-label">Correct</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <?php $__errorArgs = ['options'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="alert alert-danger"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-secondary" id="add-option">
                            <i class="bi bi-plus-lg"></i>
                            Add Another Option
                        </button>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="<?php echo e(route('admin.questions.index')); ?>" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i>
                            Back to List
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i>
                            Create Question
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    let optionIndex = <?php echo e(count($oldOptions ?? [])); ?>;
    
    // Add option functionality
    document.getElementById('add-option').addEventListener('click', function() {
        const container = document.getElementById('options-container');
        const optionHtml = `
            <div class="option-item mb-3 p-3 border rounded">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">Option ${optionIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-option">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
                
                <div class="row">
                    <div class="col-md-8">
                        <input type="text" 
                               class="form-control" 
                               name="options[${optionIndex}][option_text]" 
                               placeholder="Enter option text" 
                               required>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="options[${optionIndex}][direction]">
                            <option value="ltr">LTR</option>
                            <option value="rtl">RTL</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <div class="form-check form-switch">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   name="options[${optionIndex}][is_correct]" 
                                   value="1">
                            <label class="form-check-label">Correct</label>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', optionHtml);
        optionIndex++;
        updateOptionNumbers();
    });
    
    // Remove option functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-option')) {
            e.target.closest('.option-item').remove();
            updateOptionNumbers();
        }
    });
    
    // Update option numbers
    function updateOptionNumbers() {
        const options = document.querySelectorAll('.option-item');
        options.forEach((option, index) => {
            option.querySelector('h6').textContent = `Option ${index + 1}`;
        });
    }
    
    // True/False type handling
    document.getElementById('type').addEventListener('change', function() {
        const container = document.getElementById('options-container');
        if (this.value === 'true_false') {
            // Clear existing options and add True/False options
            container.innerHTML = `
                <div class="option-item mb-3 p-3 border rounded">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Option 1</h6>
                    </div>
                    <div class="row">
                        <div class="col-md-8">
                            <input type="text" class="form-control" name="options[0][option_text]" value="True" readonly>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" name="options[0][direction]">
                                <option value="ltr">LTR</option>
                                <option value="rtl">RTL</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="options[0][is_correct]" value="1">
                                <label class="form-check-label">Correct</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="option-item mb-3 p-3 border rounded">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Option 2</h6>
                    </div>
                    <div class="row">
                        <div class="col-md-8">
                            <input type="text" class="form-control" name="options[1][option_text]" value="False" readonly>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" name="options[1][direction]">
                                <option value="ltr">LTR</option>
                                <option value="rtl">RTL</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="options[1][is_correct]" value="1">
                                <label class="form-check-label">Correct</label>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('add-option').style.display = 'none';
        } else {
            document.getElementById('add-option').style.display = 'inline-block';
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp5\htdocs\eduexamin\codes\resources\views/admin/questions/create.blade.php ENDPATH**/ ?>