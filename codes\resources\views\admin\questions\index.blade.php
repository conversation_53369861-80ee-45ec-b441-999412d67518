@extends('admin.layouts.app')

@section('title', 'Questions')
@section('page-title', 'Question Management')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-question-circle"></i>
                    All Questions
                </h6>
                <a href="{{ route('admin.questions.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i>
                    Add New Question
                </a>
            </div>
            <div class="card-body">
                @if($questions->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Question</th>
                                    <th>Subject</th>
                                    <th>Type</th>
                                    <th>Options</th>
                                    <th>Created By</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($questions as $question)
                                    <tr>
                                        <td>{{ $question->id }}</td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 200px;" title="{{ $question->question_text }}">
                                                {{ $question->question_text }}
                                            </div>
                                            @if($question->direction === 'rtl')
                                                <small class="badge bg-secondary">RTL</small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $question->subject->name }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $question->type === 'mcq' ? 'primary' : 'success' }}">
                                                {{ $question->type === 'mcq' ? 'Multiple Choice' : 'True/False' }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">{{ $question->options_count ?? 0 }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="bi bi-person text-white"></i>
                                                </div>
                                                <small>{{ $question->admin->name }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <small>{{ $question->created_at->format('M d, Y') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.questions.show', $question) }}" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.questions.edit', $question) }}" 
                                                   class="btn btn-sm btn-outline-warning" 
                                                   title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form method="POST" 
                                                      action="{{ route('admin.questions.destroy', $question) }}" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this question?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $questions->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-question-circle text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No questions found.</p>
                        <a href="{{ route('admin.questions.create') }}" class="btn btn-primary">
                            <i class="bi bi-plus-lg"></i>
                            Add First Question
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-sm {
        width: 1.5rem;
        height: 1.5rem;
    }
</style>
@endpush
