@extends('admin.layouts.app')

@section('title', 'Student Details')
@section('page-title', 'Student Details')

@section('content')
<div class="row">
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-circle"></i>
                    Student Information
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="avatar-lg bg-info rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                    <i class="bi bi-person text-white" style="font-size: 2rem;"></i>
                </div>
                <h5 class="card-title">{{ $student->name }}</h5>
                <p class="text-muted">{{ $student->email }}</p>
                
                @if($student->email_verified_at)
                    <span class="badge bg-success mb-3">Email Verified</span>
                @else
                    <span class="badge bg-warning mb-3">Email Not Verified</span>
                @endif

                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 text-primary">{{ $student->attempts->count() }}</div>
                        <small class="text-muted">Total Attempts</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success">{{ $student->savedExams->count() }}</div>
                        <small class="text-muted">Saved Exams</small>
                    </div>
                </div>

                <hr>

                <div class="text-muted">
                    <small>
                        <i class="bi bi-calendar"></i>
                        Joined {{ $student->created_at->format('M d, Y') }}
                    </small>
                </div>
                
                @if($student->email_verified_at)
                    <div class="text-muted mt-1">
                        <small>
                            <i class="bi bi-check-circle"></i>
                            Verified {{ $student->email_verified_at->format('M d, Y') }}
                        </small>
                    </div>
                @endif
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.students.index') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to List
                    </a>
                    <a href="{{ route('admin.students.edit', $student) }}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Exam Attempts -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clipboard-check"></i>
                    Exam Attempts ({{ $student->attempts->where('type', 'exam')->count() }})
                </h6>
            </div>
            <div class="card-body">
                @php $examAttempts = $student->attempts->where('type', 'exam') @endphp
                @if($examAttempts->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Exam</th>
                                    <th>Score</th>
                                    <th>Started</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($examAttempts->take(10) as $attempt)
                                    <tr>
                                        <td>
                                            <div class="fw-bold">{{ $attempt->exam->title }}</div>
                                            <small class="text-muted">{{ $attempt->exam->subject->name }}</small>
                                        </td>
                                        <td>
                                            @if($attempt->score !== null)
                                                <span class="fw-bold text-success">{{ $attempt->score }}%</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $attempt->started_at->format('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                <span class="badge bg-success">Completed</span>
                                            @else
                                                <span class="badge bg-warning">In Progress</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="bi bi-clipboard-check text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No exam attempts yet.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Quiz Attempts -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-question-circle"></i>
                    Quiz Attempts ({{ $student->attempts->where('type', 'quiz')->count() }})
                </h6>
            </div>
            <div class="card-body">
                @php $quizAttempts = $student->attempts->where('type', 'quiz') @endphp
                @if($quizAttempts->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Subject</th>
                                    <th>Score</th>
                                    <th>Started</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($quizAttempts->take(10) as $attempt)
                                    <tr>
                                        <td>
                                            <div class="fw-bold">{{ $attempt->subject->name }}</div>
                                            <small class="text-muted">Quiz</small>
                                        </td>
                                        <td>
                                            @if($attempt->score !== null)
                                                <span class="fw-bold text-success">{{ $attempt->score }}%</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $attempt->started_at->format('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                <span class="badge bg-success">Completed</span>
                                            @else
                                                <span class="badge bg-warning">In Progress</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <a class="btn btn-primary" href="{{route('admin.students.attempts', $student)}}">All attemps</a>
                @else
                    <div class="text-center py-3">
                        <i class="bi bi-question-circle text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No quiz attempts yet.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Saved Exams -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-bookmark"></i>
                    Saved Exams ({{ $student->savedExams->count() }})
                </h6>
            </div>
            <div class="card-body">
                @if($student->savedExams->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Exam</th>
                                    <th>Subject</th>
                                    <th>Duration</th>
                                    <th>Saved At</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($student->savedExams as $exam)
                                    <tr>
                                        <td>
                                            <div class="fw-bold">{{ $exam->title }}</div>
                                            @if($exam->description)
                                                <small class="text-muted">{{ Str::limit($exam->description, 50) }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $exam->subject->name }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">{{ $exam->duration }} min</span>
                                        </td>
                                        <td>
                                            <small>{{ $exam->pivot->created_at->format('M d, Y') }}</small>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="bi bi-bookmark text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No saved exams yet.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-lg {
        width: 4rem;
        height: 4rem;
    }
</style>
@endpush
