<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Student;
use Illuminate\Http\Request;

class StudentController extends Controller
{
    public function index()
    {
        return response()->json(Student::with('attempts')->paginate(15));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:students',
            'password' => 'required|string|min:8',
        ]);

        $student = Student::create($request->all());
        $student->assignRole('student');

        return response()->json($student, 201);
    }

    public function show(Student $student)
    {
        return response()->json($student->load(['attempts.exam', 'attempts.subject']));
    }

    public function update(Request $request, Student $student)
    {
        $request->validate([
            'name' => 'string|max:255',
            'email' => 'string|email|max:255|unique:students,email,' . $student->id,
        ]);

        $student->update($request->only(['name', 'email']));

        return response()->json($student);
    }

    public function destroy(Student $student)
    {
        $student->delete();
        return response()->json(null, 204);
    }
}