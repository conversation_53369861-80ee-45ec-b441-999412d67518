<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Question;
use App\Models\Subject;
use App\Models\Option;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class QuestionController extends Controller
{
    /**
     * Display a listing of questions.
     */
    public function index()
    {
        $questions = Question::with(['subject', 'admin', 'options'])
            ->withCount('options')
            ->orderBy('created_at', 'desc')
            ->paginate(15);
            
        return view('admin.questions.index', compact('questions'));
    }

    /**
     * Show the form for creating a new question.
     */
    public function create()
    {
        $subjects = Subject::orderBy('name')->get();
        return view('admin.questions.create', compact('subjects'));
    }

    /**
     * Store a newly created question in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'question_text' => 'required|string',
            'direction' => 'required|in:ltr,rtl',
            'type' => 'required|in:mcq,true_false',
            'options' => 'required|array|min:2',
            'options.*.option_text' => 'required|string',
            'options.*.direction' => 'required|in:ltr,rtl',
            'options.*.is_correct' => 'required|boolean',
        ]);

        // Validate that at least one option is correct
        $correctOptions = collect($request->options)->where('is_correct', true);
        if ($correctOptions->count() === 0) {
            return back()->withErrors(['options' => 'At least one option must be marked as correct.'])
                ->withInput();
        }

        DB::transaction(function () use ($request) {
            $question = Question::create([
                'subject_id' => $request->subject_id,
                'question_text' => $request->question_text,
                'direction' => $request->direction,
                'type' => $request->type,
                'created_by' => auth('admin')->id(),
            ]);

            foreach ($request->options as $optionData) {
                Option::create([
                    'question_id' => $question->id,
                    'option_text' => $optionData['option_text'],
                    'direction' => $optionData['direction'],
                    'is_correct' => $optionData['is_correct'],
                ]);
            }
        });

        return redirect()->route('admin.questions.index')
            ->with('success', 'Question created successfully.');
    }

    /**
     * Display the specified question.
     */
    public function show(Question $question)
    {
        $question->load(['subject', 'admin', 'options', 'exams']);
        return view('admin.questions.show', compact('question'));
    }

    /**
     * Show the form for editing the specified question.
     */
    public function edit(Question $question)
    {
        $subjects = Subject::orderBy('name')->get();
        $question->load('options');
        return view('admin.questions.edit', compact('question', 'subjects'));
    }

    /**
     * Update the specified question in storage.
     */
    public function update(Request $request, Question $question)
    {
        $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'question_text' => 'required|string',
            'direction' => 'required|in:ltr,rtl',
            'type' => 'required|in:mcq,true_false',
            'options' => 'required|array|min:2',
            'options.*.option_text' => 'required|string',
            'options.*.direction' => 'required|in:ltr,rtl',
            'options.*.is_correct' => 'required|boolean',
        ]);

        // Validate that at least one option is correct
        $correctOptions = collect($request->options)->where('is_correct', true);
        if ($correctOptions->count() === 0) {
            return back()->withErrors(['options' => 'At least one option must be marked as correct.'])
                ->withInput();
        }

        DB::transaction(function () use ($request, $question) {
            $question->update([
                'subject_id' => $request->subject_id,
                'question_text' => $request->question_text,
                'direction' => $request->direction,
                'type' => $request->type,
            ]);

            // Delete existing options and create new ones
            $question->options()->delete();

            foreach ($request->options as $optionData) {
                Option::create([
                    'question_id' => $question->id,
                    'option_text' => $optionData['option_text'],
                    'direction' => $optionData['direction'],
                    'is_correct' => $optionData['is_correct'],
                ]);
            }
        });

        return redirect()->route('admin.questions.index')
            ->with('success', 'Question updated successfully.');
    }

    /**
     * Remove the specified question from storage.
     */
    public function destroy(Question $question)
    {
        // Check if question is used in any exams
        if ($question->exams()->count() > 0) {
            return redirect()->route('admin.questions.index')
                ->with('error', 'Cannot delete question that is used in exams.');
        }

        $question->delete();

        return redirect()->route('admin.questions.index')
            ->with('success', 'Question deleted successfully.');
    }
}
