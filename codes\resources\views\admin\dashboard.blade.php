@extends('admin.layouts.app')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Subjects
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ $stats['total_subjects'] }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-book text-primary" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total Questions
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ $stats['total_questions'] }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-question-circle text-success" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Students
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ $stats['total_students'] }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-badge text-info" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Total Exams
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ $stats['total_exams'] }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clipboard-check text-warning" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Statistics Row -->
<div class="row mb-4">
    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-graph-up"></i>
                    Quick Stats
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 text-center">
                        <div class="h4 mb-0 text-primary">{{ $stats['total_attempts'] }}</div>
                        <small class="text-muted">Total Attempts</small>
                    </div>
                    <div class="col-6 text-center">
                        <div class="h4 mb-0 text-success">{{ $recent_attempts->count() }}</div>
                        <small class="text-muted">Recent Activity</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-6 col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-speedometer2"></i>
                    System Status
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-2">
                    <div class="flex-grow-1">
                        <small class="text-muted">Database</small>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: 100%"></div>
                        </div>
                    </div>
                    <span class="badge bg-success ms-2">Online</span>
                </div>
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <small class="text-muted">System</small>
                        <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-success" style="width: 100%"></div>
                        </div>
                    </div>
                    <span class="badge bg-success ms-2">Active</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Attempts Table -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clock-history"></i>
                    Recent Attempts
                </h6>
                <a href="{{ route('admin.students.index') }}" class="btn btn-sm btn-outline-primary">
                    View All Students
                </a>
            </div>
            <div class="card-body">
                @if($recent_attempts->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Student</th>
                                    <th>Exam/Subject</th>
                                    <th>Type</th>
                                    <th>Score</th>
                                    <th>Started At</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recent_attempts as $attempt)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="bi bi-person text-white"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $attempt->student->name }}</div>
                                                    <small class="text-muted">{{ $attempt->student->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($attempt->exam)
                                                <span class="fw-bold">{{ $attempt->exam->title }}</span>
                                            @elseif($attempt->subject)
                                                <span class="fw-bold">{{ $attempt->subject->name }}</span>
                                                <small class="text-muted d-block">Quiz</small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $attempt->type === 'exam' ? 'primary' : 'info' }}">
                                                {{ ucfirst($attempt->type) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($attempt->score !== null)
                                                <span class="fw-bold text-success">{{ $attempt->score }}%</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $attempt->started_at->format('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                <span class="badge bg-success">Completed</span>
                                            @else
                                                <span class="badge bg-warning">In Progress</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No recent attempts found.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .avatar-sm {
        width: 2rem;
        height: 2rem;
    }
</style>
@endpush
