@extends('admin.layouts.app')

@section('title', 'Subjects')
@section('page-title', 'Subject Management')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-book"></i>
                    All Subjects
                </h6>
                <a href="{{ route('admin.subjects.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i>
                    Add New Subject
                </a>
            </div>
            <div class="card-body">
                @if($subjects->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Questions</th>
                                    <th>Exams</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($subjects as $subject)
                                    <tr>
                                        <td>{{ $subject->id }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-success rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="bi bi-book text-white"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $subject->name }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($subject->description)
                                                <span class="text-muted">{{ Str::limit($subject->description, 50) }}</span>
                                            @else
                                                <span class="text-muted">No description</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $subject->questions_count ?? 0 }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">{{ $subject->exams_count ?? 0 }}</span>
                                        </td>
                                        <td>
                                            <small>{{ $subject->created_at->format('M d, Y') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.subjects.show', $subject) }}" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.subjects.edit', $subject) }}" 
                                                   class="btn btn-sm btn-outline-warning" 
                                                   title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form method="POST" 
                                                      action="{{ route('admin.subjects.destroy', $subject) }}" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this subject?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $subjects->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-book text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No subjects found.</p>
                        <a href="{{ route('admin.subjects.create') }}" class="btn btn-primary">
                            <i class="bi bi-plus-lg"></i>
                            Add First Subject
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-sm {
        width: 2rem;
        height: 2rem;
    }
</style>
@endpush
