@extends('admin.layouts.app')

@section('title', 'Attempts')
@section('page-title', 'Attempt Management')

@section('content')
<div class="row">
    <div class="col-12">
        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-funnel"></i>
                    Filters
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('admin.attempts.index') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="type" class="form-label">Type</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">All Types</option>
                                <option value="exam" {{ request('type') === 'exam' ? 'selected' : '' }}>Exam</option>
                                <option value="quiz" {{ request('type') === 'quiz' ? 'selected' : '' }}>Quiz</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed</option>
                                <option value="in_progress" {{ request('status') === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="student_id" class="form-label">Student</label>
                            <select class="form-select" id="student_id" name="student_id">
                                <option value="">All Students</option>
                                @foreach($students as $student)
                                    <option value="{{ $student->id }}" {{ request('student_id') == $student->id ? 'selected' : '' }}>
                                        {{ $student->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="Student name or email">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <label for="exam_id" class="form-label">Exam</label>
                            <select class="form-select" id="exam_id" name="exam_id">
                                <option value="">All Exams</option>
                                @foreach($exams as $exam)
                                    <option value="{{ $exam->id }}" {{ request('exam_id') == $exam->id ? 'selected' : '' }}>
                                        {{ $exam->title }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="subject_id" class="form-label">Subject</label>
                            <select class="form-select" id="subject_id" name="subject_id">
                                <option value="">All Subjects</option>
                                @foreach($subjects as $subject)
                                    <option value="{{ $subject->id }}" {{ request('subject_id') == $subject->id ? 'selected' : '' }}>
                                        {{ $subject->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-search"></i>
                                Filter
                            </button>
                            <a href="{{ route('admin.attempts.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i>
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Attempts Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clipboard-data"></i>
                    All Attempts ({{ $attempts->total() }})
                </h6>
            </div>
            <div class="card-body">
                @if($attempts->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Student</th>
                                    <th>Type</th>
                                    <th>Exam/Subject</th>
                                    <th>Score</th>
                                    <th>Started</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($attempts as $attempt)
                                    <tr>
                                        <td>{{ $attempt->id }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="bi bi-person text-white"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $attempt->student->name }}</div>
                                                    <small class="text-muted">{{ $attempt->student->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $attempt->type === 'exam' ? 'primary' : 'info' }}">
                                                {{ ucfirst($attempt->type) }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($attempt->type === 'exam' && $attempt->exam)
                                                <div class="fw-bold">{{ $attempt->exam->title }}</div>
                                                <small class="text-muted">{{ $attempt->exam->subject->name }}</small>
                                            @elseif($attempt->type === 'quiz' && $attempt->subject)
                                                <div class="fw-bold">{{ $attempt->subject->name }}</div>
                                                <small class="text-muted">Quiz</small>
                                            @else
                                                <span class="text-muted">N/A</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attempt->score !== null)
                                                <span class="fw-bold text-{{ $attempt->score >= 60 ? 'success' : 'danger' }}">
                                                    {{ $attempt->score }}%
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $attempt->started_at->format('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                @php
                                                    $duration = $attempt->started_at->diffInMinutes($attempt->submitted_at);
                                                @endphp
                                                <small>{{ $duration }} min</small>
                                            @else
                                                @php
                                                    $duration = $attempt->started_at->diffInMinutes(now());
                                                @endphp
                                                <small class="text-warning">{{ $duration }} min (ongoing)</small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                <span class="badge bg-success">Completed</span>
                                            @else
                                                <span class="badge bg-warning">In Progress</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.attempts.show', $attempt) }}" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <form method="POST" 
                                                      action="{{ route('admin.attempts.destroy', $attempt) }}" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this attempt?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $attempts->appends(request()->query())->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-clipboard-data text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No attempts found.</p>
                        @if(request()->hasAny(['type', 'status', 'student_id', 'exam_id', 'subject_id', 'search']))
                            <a href="{{ route('admin.attempts.index') }}" class="btn btn-outline-primary">
                                <i class="bi bi-x-circle"></i>
                                Clear Filters
                            </a>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-sm {
        width: 1.5rem;
        height: 1.5rem;
    }
</style>
@endpush
