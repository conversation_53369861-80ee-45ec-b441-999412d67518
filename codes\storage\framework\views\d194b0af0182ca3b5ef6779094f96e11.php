<?php $__env->startSection('title', 'Attempts'); ?>
<?php $__env->startSection('page-title', 'Attempt Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-funnel"></i>
                    Filters
                </h6>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('admin.attempts.index')); ?>">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="type" class="form-label">Type</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">All Types</option>
                                <option value="exam" <?php echo e(request('type') === 'exam' ? 'selected' : ''); ?>>Exam</option>
                                <option value="quiz" <?php echo e(request('type') === 'quiz' ? 'selected' : ''); ?>>Quiz</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="completed" <?php echo e(request('status') === 'completed' ? 'selected' : ''); ?>>Completed</option>
                                <option value="in_progress" <?php echo e(request('status') === 'in_progress' ? 'selected' : ''); ?>>In Progress</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="student_id" class="form-label">Student</label>
                            <select class="form-select" id="student_id" name="student_id">
                                <option value="">All Students</option>
                                <?php $__currentLoopData = $students; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($student->id); ?>" <?php echo e(request('student_id') == $student->id ? 'selected' : ''); ?>>
                                        <?php echo e($student->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo e(request('search')); ?>" placeholder="Student name or email">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <label for="exam_id" class="form-label">Exam</label>
                            <select class="form-select" id="exam_id" name="exam_id">
                                <option value="">All Exams</option>
                                <?php $__currentLoopData = $exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($exam->id); ?>" <?php echo e(request('exam_id') == $exam->id ? 'selected' : ''); ?>>
                                        <?php echo e($exam->title); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="subject_id" class="form-label">Subject</label>
                            <select class="form-select" id="subject_id" name="subject_id">
                                <option value="">All Subjects</option>
                                <?php $__currentLoopData = $subjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($subject->id); ?>" <?php echo e(request('subject_id') == $subject->id ? 'selected' : ''); ?>>
                                        <?php echo e($subject->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-search"></i>
                                Filter
                            </button>
                            <a href="<?php echo e(route('admin.attempts.index')); ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i>
                                Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Attempts Table -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clipboard-data"></i>
                    All Attempts (<?php echo e($attempts->total()); ?>)
                </h6>
            </div>
            <div class="card-body">
                <?php if($attempts->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Student</th>
                                    <th>Type</th>
                                    <th>Exam/Subject</th>
                                    <th>Score</th>
                                    <th>Started</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $attempts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attempt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($attempt->id); ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="bi bi-person text-white"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo e($attempt->student->name); ?></div>
                                                    <small class="text-muted"><?php echo e($attempt->student->email); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo e($attempt->type === 'exam' ? 'primary' : 'info'); ?>">
                                                <?php echo e(ucfirst($attempt->type)); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php if($attempt->type === 'exam' && $attempt->exam): ?>
                                                <div class="fw-bold"><?php echo e($attempt->exam->title); ?></div>
                                                <small class="text-muted"><?php echo e($attempt->exam->subject->name); ?></small>
                                            <?php elseif($attempt->type === 'quiz' && $attempt->subject): ?>
                                                <div class="fw-bold"><?php echo e($attempt->subject->name); ?></div>
                                                <small class="text-muted">Quiz</small>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($attempt->score !== null): ?>
                                                <span class="fw-bold text-<?php echo e($attempt->score >= 60 ? 'success' : 'danger'); ?>">
                                                    <?php echo e($attempt->score); ?>%
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo e($attempt->started_at->format('M d, Y H:i')); ?></small>
                                        </td>
                                        <td>
                                            <?php if($attempt->submitted_at): ?>
                                                <?php
                                                    $duration = $attempt->started_at->diffInMinutes($attempt->submitted_at);
                                                ?>
                                                <small><?php echo e($duration); ?> min</small>
                                            <?php else: ?>
                                                <?php
                                                    $duration = $attempt->started_at->diffInMinutes(now());
                                                ?>
                                                <small class="text-warning"><?php echo e($duration); ?> min (ongoing)</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($attempt->submitted_at): ?>
                                                <span class="badge bg-success">Completed</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">In Progress</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.attempts.show', $attempt)); ?>" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <form method="POST" 
                                                      action="<?php echo e(route('admin.attempts.destroy', $attempt)); ?>" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this attempt?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($attempts->appends(request()->query())->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-clipboard-data text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No attempts found.</p>
                        <?php if(request()->hasAny(['type', 'status', 'student_id', 'exam_id', 'subject_id', 'search'])): ?>
                            <a href="<?php echo e(route('admin.attempts.index')); ?>" class="btn btn-outline-primary">
                                <i class="bi bi-x-circle"></i>
                                Clear Filters
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .avatar-sm {
        width: 1.5rem;
        height: 1.5rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp5\htdocs\eduexamin\codes\resources\views/admin/attempts/index.blade.php ENDPATH**/ ?>