<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('attempt_options', function (Blueprint $table) {
            $table->id();
            $table->foreignId('attempt_question_id')->constrained()->onDelete('cascade');
            $table->foreignId('option_id')->constrained()->onDelete('cascade');
            $table->string('option_text');
            $table->enum('direction', ['ltr', 'rtl'])->default('ltr');
            $table->boolean('is_correct');
            $table->boolean('selected')->default(false);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('attempt_options');
    }
};