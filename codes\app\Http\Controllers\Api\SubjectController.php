<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use Illuminate\Http\Request;

class SubjectController extends Controller
{
    public function index()
    {
        return response()->json(Subject::withCount(['questions', 'exams'])->get());
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $subject = Subject::create($request->all());

        return response()->json($subject, 201);
    }

    public function show(Subject $subject)
    {
        return response()->json($subject->load(['questions.options', 'exams']));
    }

    public function update(Request $request, Subject $subject)
    {
        $request->validate([
            'name' => 'string|max:255',
            'description' => 'nullable|string',
        ]);

        $subject->update($request->all());

        return response()->json($subject);
    }

    public function destroy(Subject $subject)
    {
        $subject->delete();
        return response()->json(null, 204);
    }
}