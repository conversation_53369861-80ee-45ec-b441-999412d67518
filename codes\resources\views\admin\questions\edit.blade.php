@extends('admin.layouts.app')

@section('title', 'Edit Question')
@section('page-title', 'Edit Question')

@section('content')
<div class="row justify-content-center">
    <div class="col-md-10 col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pencil"></i>
                    Edit Question
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.questions.update', $question) }}" id="questionForm">
                    @csrf
                    @method('PUT')

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subject_id" class="form-label">Subject</label>
                                <select class="form-select @error('subject_id') is-invalid @enderror" 
                                        id="subject_id" 
                                        name="subject_id" 
                                        required>
                                    <option value="">Select Subject</option>
                                    @foreach($subjects as $subject)
                                        <option value="{{ $subject->id }}" 
                                                {{ old('subject_id', $question->subject_id) == $subject->id ? 'selected' : '' }}>
                                            {{ $subject->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('subject_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">Question Type</label>
                                <select class="form-select @error('type') is-invalid @enderror" 
                                        id="type" 
                                        name="type" 
                                        required>
                                    <option value="">Select Type</option>
                                    <option value="mcq" {{ old('type', $question->type) == 'mcq' ? 'selected' : '' }}>Multiple Choice</option>
                                    <option value="true_false" {{ old('type', $question->type) == 'true_false' ? 'selected' : '' }}>True/False</option>
                                </select>
                                @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="question_text" class="form-label">Question Text</label>
                        <textarea class="form-control @error('question_text') is-invalid @enderror" 
                                  id="question_text" 
                                  name="question_text" 
                                  rows="3" 
                                  required
                                  placeholder="Enter the question text">{{ old('question_text', $question->question_text) }}</textarea>
                        @error('question_text')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="direction" class="form-label">Text Direction</label>
                        <select class="form-select @error('direction') is-invalid @enderror" 
                                id="direction" 
                                name="direction" 
                                required>
                            <option value="ltr" {{ old('direction', $question->direction) == 'ltr' ? 'selected' : '' }}>Left to Right (LTR)</option>
                            <option value="rtl" {{ old('direction', $question->direction) == 'rtl' ? 'selected' : '' }}>Right to Left (RTL)</option>
                        </select>
                        @error('direction')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <hr>
                    <h6 class="text-primary">Answer Options</h6>
                    <p class="text-muted small">Add at least 2 options and mark the correct answer(s).</p>

                    <div id="options-container">
                        @php
                            $oldOptions = old('options');
                            if (!$oldOptions) {
                                $oldOptions = $question->options->map(function($option) {
                                    return [
                                        'option_text' => $option->option_text,
                                        'direction' => $option->direction,
                                        'is_correct' => $option->is_correct ? '1' : '0'
                                    ];
                                })->toArray();
                            }
                        @endphp
                        
                        @foreach($oldOptions as $index => $option)
                            <div class="option-item mb-3 p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">Option {{ $index + 1 }}</h6>
                                    @if($index > 1)
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-option">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    @endif
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-8">
                                        <input type="text" 
                                               class="form-control @error('options.'.$index.'.option_text') is-invalid @enderror" 
                                               name="options[{{ $index }}][option_text]" 
                                               value="{{ $option['option_text'] }}"
                                               placeholder="Enter option text" 
                                               required>
                                        @error('options.'.$index.'.option_text')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select" name="options[{{ $index }}][direction]">
                                            <option value="ltr" {{ $option['direction'] == 'ltr' ? 'selected' : '' }}>LTR</option>
                                            <option value="rtl" {{ $option['direction'] == 'rtl' ? 'selected' : '' }}>RTL</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   name="options[{{ $index }}][is_correct]" 
                                                   value="1"
                                                   {{ $option['is_correct'] == '1' ? 'checked' : '' }}>
                                            <label class="form-check-label">Correct</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    @error('options')
                        <div class="alert alert-danger">{{ $message }}</div>
                    @enderror

                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-secondary" id="add-option">
                            <i class="bi bi-plus-lg"></i>
                            Add Another Option
                        </button>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.questions.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i>
                            Back to List
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i>
                            Update Question
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let optionIndex = {{ count($oldOptions ?? []) }};
    
    // Add option functionality
    document.getElementById('add-option').addEventListener('click', function() {
        const container = document.getElementById('options-container');
        const optionHtml = `
            <div class="option-item mb-3 p-3 border rounded">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">Option ${optionIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-option">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
                
                <div class="row">
                    <div class="col-md-8">
                        <input type="text" 
                               class="form-control" 
                               name="options[${optionIndex}][option_text]" 
                               placeholder="Enter option text" 
                               required>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="options[${optionIndex}][direction]">
                            <option value="ltr">LTR</option>
                            <option value="rtl">RTL</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <div class="form-check form-switch">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   name="options[${optionIndex}][is_correct]" 
                                   value="1">
                            <label class="form-check-label">Correct</label>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.insertAdjacentHTML('beforeend', optionHtml);
        optionIndex++;
        updateOptionNumbers();
    });
    
    // Remove option functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-option')) {
            e.target.closest('.option-item').remove();
            updateOptionNumbers();
        }
    });
    
    // Update option numbers
    function updateOptionNumbers() {
        const options = document.querySelectorAll('.option-item');
        options.forEach((option, index) => {
            option.querySelector('h6').textContent = `Option ${index + 1}`;
        });
    }
});
</script>
@endpush
