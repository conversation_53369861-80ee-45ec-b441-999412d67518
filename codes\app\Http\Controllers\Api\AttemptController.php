<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\Subject;
use App\Models\Attempt;
use App\Models\Question;
use App\Models\ExamRegistration;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AttemptController extends Controller
{
    public function startExam(Request $request, Exam $exam)
    {
        $now = Carbon::now();
        
        if ($now->lt($exam->start_time) || $now->gt($exam->end_time)) {
            return response()->json(['message' => 'Exam is not available'], 403);
        }

        // Ensure the student is registered for the exam
        // Removed: use App\Models\ExamRegistration;
        // Removed: $isRegistered = ExamRegistration::where('exam_id', $exam->id)
        //     ->where('student_id', auth()->id())
        //     ->exists();
        // if (!$isRegistered) {
        //     return response()->json(['message' => 'Not registered for this exam'], 403);
        // }

        // Check if student already has an active attempt
        $existingAttempt = Attempt::where('student_id', auth()->id())
            ->where('exam_id', $exam->id)
            ->whereNull('submitted_at')
            ->first();

        if ($existingAttempt) {
            return response()->json($existingAttempt->load('attemptQuestions.attemptOptions'));
        }

        $attempt = Attempt::create([
            'student_id' => auth()->id(),
            'exam_id' => $exam->id,
            'type' => 'exam',
            'started_at' => $now,
        ]);

        $this->createAttemptSnapshot($attempt, $exam->questions);

        return response()->json($attempt->load('attemptQuestions.attemptOptions'), 201);
    }

    public function startQuiz(Request $request, Subject $subject)
    {
        $request->validate([
            'question_count' => 'integer|min:1|max:50',
        ]);

        $questionCount = $request->question_count ?? 10;
        $questions = $subject->questions()
            ->with('options')
            ->inRandomOrder()
            ->take($questionCount)
            ->get();

        if ($questions->count() < $questionCount) {
            return response()->json(['message' => 'Not enough questions available'], 400);
        }

        $attempt = Attempt::create([
            'student_id' => auth()->id(),
            'subject_id' => $subject->id,
            'type' => 'quiz',
            'started_at' => Carbon::now(),
        ]);

        $this->createAttemptSnapshot($attempt, $questions);

        return response()->json($attempt->load('attemptQuestions.attemptOptions'), 201);
    }

    public function show(Attempt $attempt)
    {
        if ($attempt->student_id !== auth()->id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return response()->json($attempt->load([
            'attemptQuestions.attemptOptions',
            'exam.subject',
            'subject'
        ]));
    }

    public function saveAnswer(Request $request, Attempt $attempt)
    {
        if ($attempt->student_id !== auth()->id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if ($attempt->submitted_at) {
            return response()->json(['message' => 'Attempt already submitted'], 400);
        }

        $request->validate([
            'attempt_question_id' => 'required|exists:attempt_questions,id',
            'selected_option_ids' => 'required|array',
            'selected_option_ids.*' => 'exists:attempt_options,id',
        ]);

        $attemptQuestion = $attempt->attemptQuestions()
            ->where('id', $request->attempt_question_id)
            ->first();

        if (!$attemptQuestion) {
            return response()->json(['message' => 'Question not found'], 404);
        }

        // Reset all selections for this question
        $attemptQuestion->attemptOptions()->update(['selected' => false]);

        // Set new selections
        $attemptQuestion->attemptOptions()
            ->whereIn('id', $request->selected_option_ids)
            ->update(['selected' => true]);

        return response()->json(['message' => 'Answer saved']);
    }

    public function submit(Attempt $attempt)
    {
        if ($attempt->student_id !== auth()->id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if ($attempt->submitted_at) {
            return response()->json(['message' => 'Attempt already submitted'], 400);
        }

        $attempt->update([
            'submitted_at' => Carbon::now(),
            'score' => $this->calculateScore($attempt),
        ]);

        return response()->json($attempt->load('attemptQuestions.attemptOptions'));
    }

    public function myAttempts()
    {
        return response()->json(
            Attempt::where('student_id', auth()->id())
                ->with(['exam.subject', 'subject'])
                ->orderBy('created_at', 'desc')
                ->paginate(15)
        );
    }

    private function createAttemptSnapshot(Attempt $attempt, $questions)
    {
        foreach ($questions as $question) {
            $attemptQuestion = $attempt->attemptQuestions()->create([
                'question_id' => $question->id,
                'question_text' => $question->question_text,
                'direction' => $question->direction,
                'type' => $question->type,
            ]);

            foreach ($question->options as $option) {
                $attemptQuestion->attemptOptions()->create([
                    'option_id' => $option->id,
                    'option_text' => $option->option_text,
                    'direction' => $option->direction,
                    'is_correct' => $option->is_correct,
                    'selected' => false,
                ]);
            }
        }
    }

    private function calculateScore(Attempt $attempt)
    {
        $totalQuestions = $attempt->attemptQuestions()->count();
        $correctAnswers = 0;

        foreach ($attempt->attemptQuestions as $attemptQuestion) {
            $correctOptions = $attemptQuestion->attemptOptions()
                ->where('is_correct', true)
                ->pluck('id')
                ->sort()
                ->values();

            $selectedOptions = $attemptQuestion->attemptOptions()
                ->where('selected', true)
                ->pluck('id')
                ->sort()
                ->values();

            if ($correctOptions->toArray() === $selectedOptions->toArray()) {
                $correctAnswers++;
            }
        }

        return round(($correctAnswers / $totalQuestions) * 100);
    }
}