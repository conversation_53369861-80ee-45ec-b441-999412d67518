<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Student;
// Removed: use App\Models\ExamRegistration;

class Exam extends Model
{
    use HasFactory;

    protected $fillable = [
        'subject_id',
        'title',
        'description',
        'start_time',
        'end_time',
        'duration',
        'created_by',
    ];

    protected function casts(): array
    {
        return [
            'start_time' => 'datetime',
            'end_time' => 'datetime',
        ];
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class);
    }

    public function admin()
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    public function questions()
    {
        return $this->belongsToMany(Question::class, 'exam_questions');
    }

    public function attempts()
    {
        return $this->hasMany(Attempt::class);
    }

    // Removed: public function registrations()
    // {
    //     return $this->hasMany(ExamRegistration::class);
    // }

    // public function registeredStudents()
    // {
    //     // Removed: return $this->belongsToMany(Student::class, 'exam_registrations');
    // }
    public function savings()
    {
        return $this->hasMany(ExamSaving::class);
    }

    public function savedByStudents()
    {
        return $this->belongsToMany(Student::class, 'exam_savings')
                    ->withTimestamps();
    }

}