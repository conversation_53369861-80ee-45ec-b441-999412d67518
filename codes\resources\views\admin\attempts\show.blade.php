@extends('admin.layouts.app')

@section('title', 'Attempt Details')
@section('page-title', 'Attempt Details')

@section('content')
<div class="row">
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    Attempt Information
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg bg-{{ $attempt->type === 'exam' ? 'primary' : 'info' }} rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                        <i class="bi bi-{{ $attempt->type === 'exam' ? 'clipboard-check' : 'question-circle' }} text-white" style="font-size: 2rem;"></i>
                    </div>
                    <h6 class="card-title">{{ ucfirst($attempt->type) }} Attempt #{{ $attempt->id }}</h6>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Student</label>
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                            <i class="bi bi-person text-white"></i>
                        </div>
                        <div>
                            <div class="fw-bold">{{ $attempt->student->name }}</div>
                            <small class="text-muted">{{ $attempt->student->email }}</small>
                        </div>
                    </div>
                </div>

                @if($attempt->type === 'exam' && $attempt->exam)
                    <div class="mb-3">
                        <label class="form-label text-muted">Exam</label>
                        <div>
                            <div class="fw-bold">{{ $attempt->exam->title }}</div>
                            <small class="text-muted">{{ $attempt->exam->subject->name }}</small>
                        </div>
                    </div>
                @elseif($attempt->type === 'quiz' && $attempt->subject)
                    <div class="mb-3">
                        <label class="form-label text-muted">Subject</label>
                        <div>
                            <div class="fw-bold">{{ $attempt->subject->name }}</div>
                            <small class="text-muted">Quiz</small>
                        </div>
                    </div>
                @endif

                <div class="mb-3">
                    <label class="form-label text-muted">Score</label>
                    <div>
                        @if($attempt->score !== null)
                            <span class="badge bg-{{ $attempt->score >= 60 ? 'success' : 'danger' }} fs-6">
                                {{ $attempt->score }}%
                            </span>
                        @else
                            <span class="badge bg-secondary fs-6">Not Completed</span>
                        @endif
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Status</label>
                    <div>
                        @if($attempt->submitted_at)
                            <span class="badge bg-success fs-6">Completed</span>
                        @else
                            <span class="badge bg-warning fs-6">In Progress</span>
                        @endif
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Timeline</label>
                    <div>
                        <small class="text-muted">
                            <i class="bi bi-play-circle"></i>
                            Started: {{ $attempt->started_at->format('M d, Y H:i') }}
                        </small><br>
                        @if($attempt->submitted_at)
                            <small class="text-muted">
                                <i class="bi bi-stop-circle"></i>
                                Completed: {{ $attempt->submitted_at->format('M d, Y H:i') }}
                            </small><br>
                            <small class="text-success">
                                <i class="bi bi-clock"></i>
                                Duration: {{ $attempt->started_at->diffInMinutes($attempt->submitted_at) }} minutes
                            </small>
                        @else
                            <small class="text-warning">
                                <i class="bi bi-clock"></i>
                                Ongoing: {{ $attempt->started_at->diffInMinutes(now()) }} minutes
                            </small>
                        @endif
                    </div>
                </div>

                <div class="row text-center mb-3">
                    <div class="col-4">
                        <div class="h5 text-primary">{{ $statistics['total_questions'] }}</div>
                        <small class="text-muted">Total</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 text-success">{{ $statistics['correct_answers'] }}</div>
                        <small class="text-muted">Correct</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 text-danger">{{ $statistics['incorrect_answers'] }}</div>
                        <small class="text-muted">Incorrect</small>
                    </div>
                </div>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="h6 text-info">{{ $statistics['answered_questions'] }}</div>
                        <small class="text-muted">Answered</small>
                    </div>
                    <div class="col-6">
                        <div class="h6 text-warning">{{ $statistics['unanswered_questions'] }}</div>
                        <small class="text-muted">Unanswered</small>
                    </div>
                </div>

                @if($statistics['answered_questions'] > 0)
                    <div class="mt-3">
                        <label class="form-label text-muted">Accuracy</label>
                        <div class="progress">
                            <div class="progress-bar bg-{{ $statistics['accuracy'] >= 60 ? 'success' : 'danger' }}" 
                                 role="progressbar" 
                                 style="width: {{ $statistics['accuracy'] }}%">
                                {{ $statistics['accuracy'] }}%
                            </div>
                        </div>
                    </div>
                @endif
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.attempts.index') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to List
                    </a>
                    <a href="{{ route('admin.students.attempts', $attempt->student) }}" class="btn btn-outline-primary">
                        <i class="bi bi-person"></i>
                        Student Attempts
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Questions and Answers -->
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-check"></i>
                    Questions & Answers ({{ $attempt->attemptQuestions->count() }})
                </h5>
            </div>
            <div class="card-body">
                @if($attempt->attemptQuestions->count() > 0)
                    @foreach($attempt->attemptQuestions as $index => $attemptQuestion)
                        <div class="card mb-3 border-{{ $attemptQuestion->attemptOptions->where('selected', true)->where('is_correct', true)->count() > 0 && $attemptQuestion->attemptOptions->where('selected', true)->where('is_correct', false)->count() === 0 ? 'success' : ($attemptQuestion->attemptOptions->where('selected', true)->count() > 0 ? 'danger' : 'warning') }}">
                            <div class="card-header py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Question {{ $index + 1 }}</h6>
                                    @php
                                        $selectedOptions = $attemptQuestion->attemptOptions->where('selected', true);
                                        $correctSelected = $selectedOptions->where('is_correct', true)->count();
                                        $incorrectSelected = $selectedOptions->where('is_correct', false)->count();
                                        $totalCorrect = $attemptQuestion->attemptOptions->where('is_correct', true)->count();
                                        
                                        $isCorrect = $correctSelected === $totalCorrect && $incorrectSelected === 0 && $correctSelected > 0;
                                        $isAnswered = $selectedOptions->count() > 0;
                                    @endphp
                                    
                                    @if($isCorrect)
                                        <span class="badge bg-success">Correct</span>
                                    @elseif($isAnswered)
                                        <span class="badge bg-danger">Incorrect</span>
                                    @else
                                        <span class="badge bg-warning">Unanswered</span>
                                    @endif
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="p-2 bg-light rounded" dir="{{ $attemptQuestion->direction }}">
                                        <strong>{{ $attemptQuestion->question_text }}</strong>
                                    </div>
                                    @if($attemptQuestion->direction === 'rtl')
                                        <small class="text-muted">RTL</small>
                                    @endif
                                </div>

                                <div class="row">
                                    @foreach($attemptQuestion->attemptOptions as $option)
                                        <div class="col-md-6 mb-2">
                                            <div class="p-2 rounded border 
                                                {{ $option->selected && $option->is_correct ? 'bg-success bg-opacity-10 border-success' : '' }}
                                                {{ $option->selected && !$option->is_correct ? 'bg-danger bg-opacity-10 border-danger' : '' }}
                                                {{ !$option->selected && $option->is_correct ? 'bg-warning bg-opacity-10 border-warning' : '' }}
                                                {{ !$option->selected && !$option->is_correct ? 'border-light' : '' }}">
                                                
                                                <div class="d-flex align-items-center">
                                                    @if($option->selected)
                                                        <i class="bi bi-check-circle-fill text-{{ $option->is_correct ? 'success' : 'danger' }} me-2"></i>
                                                    @elseif($option->is_correct)
                                                        <i class="bi bi-exclamation-circle-fill text-warning me-2"></i>
                                                    @else
                                                        <i class="bi bi-circle me-2 text-muted"></i>
                                                    @endif
                                                    
                                                    <div dir="{{ $option->direction }}">
                                                        {{ $option->option_text }}
                                                    </div>
                                                </div>
                                                
                                                <div class="mt-1">
                                                    @if($option->selected)
                                                        <small class="badge bg-{{ $option->is_correct ? 'success' : 'danger' }}">
                                                            Selected {{ $option->is_correct ? '(Correct)' : '(Incorrect)' }}
                                                        </small>
                                                    @elseif($option->is_correct)
                                                        <small class="badge bg-warning">Correct Answer</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-question-circle text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No questions found for this attempt.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-lg {
        width: 4rem;
        height: 4rem;
    }
    .avatar-sm {
        width: 1.5rem;
        height: 1.5rem;
    }
</style>
@endpush
