<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

use Illuminate\Support\Facades\Log;

Artisan::command('send:emails', function () {
    // Example: process queued emails
    Artisan::call('queue:work --once');
    Log::info('Processed queued emails');
})->describe('Send queued emails every minute');
Artisan::command('test:cron', function () {
    // This will log a message to storage/logs/laravel.log
    Log::info('Test cron job ran successfully at ' . now());
})->describe('Test cron job for Laravel 11');