@extends('admin.layouts.app')

@section('title', 'Admins')
@section('page-title', 'Admin Management')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-people-fill"></i>
                    All Admins
                </h6>
                <a href="{{ route('admin.admins.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i>
                    Add New Admin
                </a>
            </div>
            <div class="card-body">
                @if($admins->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Questions Created</th>
                                    <th>Exams Created</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($admins as $admin)
                                    <tr>
                                        <td>{{ $admin->id }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="bi bi-person text-white"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $admin->name }}</div>
                                                    @if($admin->id === auth('admin')->id())
                                                        <small class="text-primary">(You)</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $admin->email }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ $admin->questions_count ?? 0 }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">{{ $admin->exams_count ?? 0 }}</span>
                                        </td>
                                        <td>
                                            <small>{{ $admin->created_at->format('M d, Y') }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.admins.show', $admin) }}" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.admins.edit', $admin) }}" 
                                                   class="btn btn-sm btn-outline-warning" 
                                                   title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                @if($admin->id !== auth('admin')->id())
                                                    <form method="POST" 
                                                          action="{{ route('admin.admins.destroy', $admin) }}" 
                                                          class="d-inline"
                                                          onsubmit="return confirm('Are you sure you want to delete this admin?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" 
                                                                class="btn btn-sm btn-outline-danger" 
                                                                title="Delete">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $admins->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No admins found.</p>
                        <a href="{{ route('admin.admins.create') }}" class="btn btn-primary">
                            <i class="bi bi-plus-lg"></i>
                            Add First Admin
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-sm {
        width: 2rem;
        height: 2rem;
    }
</style>
@endpush
