# EduExamin - Online Examination System

A comprehensive Laravel-based online examination system with both API endpoints and web-based admin panel for managing admins, students, subjects, questions, and exams.

## Features

- **Admin Management**: CRUD operations for administrators
- **Student Management**: Student registration and management
- **Subject Management**: Organize questions by subjects
- **Question Management**: Create MCQ and True/False questions with multiple options
- **Exam Management**: Create timed exams with question pools
- **Attempt System**: Students can take exams and quizzes
- **Authentication**: Secure API authentication using Laravel Sanctum + Web-based admin authentication
- **Role-based Access**: Different permissions for admins and students
- **Web Admin Panel**: Bootstrap-based responsive admin interface
- **Attempt Tracking**: Comprehensive attempt management and analytics

## Web Admin Panel

### Admin Authentication
- **Login**: `/admin/login` - Session-based authentication for admins
- **Password Management**: Change password functionality
- **Dashboard**: Overview with statistics and recent activities

### Admin Management (`/admin/admins`)
- **Index**: List all administrators with pagination
- **Create**: Add new administrators with role assignment
- **Show**: View administrator details with created questions/exams
- **Edit**: Update administrator information
- **Questions**: View questions created by specific admin

### Student Management (`/admin/students`)
- **Index**: List all students with search and filters
- **Create**: Add new students with email verification options
- **Show**: Detailed student profile with attempt history and saved exams
- **Edit**: Update student information and email verification status
- **Attempts**: View all attempts by specific student

### Subject Management (`/admin/subjects`)
- **Index**: List all subjects with question/exam counts
- **Create**: Add new subjects with descriptions
- **Show**: Subject details with associated questions and exams
- **Edit**: Update subject information
- **Questions**: View questions for specific subject

### Question Management (`/admin/questions`)
- **Index**: List all questions with subject filter and pagination
- **Create**: Dynamic question creation with MCQ/True-False support
- **Show**: Question details with all options and exam usage
- **Edit**: Update questions with existing options management

### Exam Management (`/admin/exams`)
- **Index**: List all exams with status indicators and statistics
- **Create**: Comprehensive exam creation with datetime pickers
- **Show**: Exam details with attached questions and recent attempts
- **Edit**: Update exam settings and schedule
- **Attach Questions**: Interface to attach/detach questions from exams
- **Results**: Detailed exam results with statistics and CSV export

### Attempt Management (`/admin/attempts`)
- **Index**: List all attempts with advanced filtering (type, status, student, exam, subject)
- **Show**: Detailed attempt view with question-by-question analysis
- **Student Attempts**: View all attempts by specific student
- **Exam Attempts**: View all attempts for specific exam
- **Delete**: Remove attempt records

## API Endpoints

### Authentication (`/auth` prefix)
1. `POST /auth/admin/login` → `AuthController@adminLogin`
2. `POST /auth/student/login` → `AuthController@studentLogin`
3. `POST /auth/student/register` → `AuthController@studentRegister`
4. `POST /auth/logout` → `AuthController@logout`

### Admin Routes (`/admin` prefix, requires admin role)
5. `GET /admin/admins` → `AdminController@index`
6. `POST /admin/admins` → `AdminController@store`
7. `GET /admin/admins/{admin}` → `AdminController@show`
8. `PUT /admin/admins/{admin}` → `AdminController@update`
9. `DELETE /admin/admins/{admin}` → `AdminController@destroy`

10. `GET /admin/students` → `StudentController@index`
11. `POST /admin/students` → `StudentController@store`
12. `GET /admin/students/{student}` → `StudentController@show`
13. `PUT /admin/students/{student}` → `StudentController@update`
14. `DELETE /admin/students/{student}` → `StudentController@destroy`

15. `GET /admin/subjects` → `SubjectController@index`
16. `POST /admin/subjects` → `SubjectController@store`
17. `GET /admin/subjects/{subject}` → `SubjectController@show`
18. `PUT /admin/subjects/{subject}` → `SubjectController@update`
19. `DELETE /admin/subjects/{subject}` → `SubjectController@destroy`

20. `GET /admin/questions` → `QuestionController@index`
21. `POST /admin/questions` → `QuestionController@store`
22. `GET /admin/questions/{question}` → `QuestionController@show`
23. `PUT /admin/questions/{question}` → `QuestionController@update`
24. `DELETE /admin/questions/{question}` → `QuestionController@destroy`

25. `GET /admin/exams` → `ExamController@index`
26. `POST /admin/exams` → `ExamController@store`
27. `GET /admin/exams/{exam}` → `ExamController@show`
28. `PUT /admin/exams/{exam}` → `ExamController@update`
29. `DELETE /admin/exams/{exam}` → `ExamController@destroy`

30. `POST /admin/exams/{exam}/questions` → `ExamController@attachQuestions`
31. `DELETE /admin/exams/{exam}/questions/{question}` → `ExamController@detachQuestion`

32. `GET /admin/analytics/dashboard` → `AdminController@dashboard`
33. `GET /admin/analytics/exam/{exam}/results` → `ExamController@results`

### Student Routes (requires student role)
34. `GET /subjects` → `SubjectController@index`
35. `GET /subjects/{subject}` → `SubjectController@show`

36. `GET /exams` → `ExamController@index`
37. `GET /exams/{exam}` → `ExamController@show`

38. `POST /exams/{exam}/attempt` → `AttemptController@startExam`
39. `POST /subjects/{subject}/quiz` → `AttemptController@startQuiz`
40. `GET /attempts/{attempt}` → `AttemptController@show`
41. `POST /attempts/{attempt}/submit` → `AttemptController@submit`
42. `POST /attempts/{attempt}/answer` → `AttemptController@saveAnswer`
43. `GET /my-attempts` → `AttemptController@myAttempts`

44. `POST /exams/{exam}/save` → `ExamController@save`
45. `DELETE /exams/{exam}/unsave` → `ExamController@unsave`
46. `GET /password` → `AuthController@updatePassword`

### Student Dashboard API (requires student role)
47. `GET /dashboard` → `DashboardController@studentDashboard`
48. `GET /attempt-history` → `DashboardController@attemptHistory`

## Student Dashboard API Details

### GET /api/dashboard
Returns comprehensive dashboard data for the authenticated student:

**Response includes:**
- **Student Info**: Basic profile information
- **Statistics**: Total attempts, completed attempts, exam/quiz counts, score statistics
- **Recent Attempts**: Last 5 attempts with details
- **Available Exams**: Currently active exams the student can take
- **Upcoming Exams**: Exams that haven't started yet
- **Subjects**: Available subjects for quiz attempts
- **Subject Performance**: Performance breakdown by subject

### GET /api/attempt-history
Returns paginated attempt history with filtering options:

**Query Parameters:**
- `type`: Filter by 'exam' or 'quiz'
- `status`: Filter by 'completed' or 'in_progress'
- `subject_id`: Filter by subject ID
- `per_page`: Number of results per page (default: 15)

## Models and Relationships

### Admin
- Has many questions (created_by)
- Has many exams (created_by)
- Uses Spatie Laravel Permission for roles

### Student  
- Has many attempts
- Has many saved exams (many-to-many)
- Uses Spatie Laravel Permission for roles

### Subject
- Has many questions
- Has many exams
- Has many attempts (for quizzes)

### Question
- Belongs to subject
- Belongs to admin (creator)
- Has many options
- Belongs to many exams (many-to-many)

### Option
- Belongs to question
- Has boolean is_correct field

### Exam
- Belongs to subject
- Belongs to admin (creator)
- Has many attempts
- Belongs to many questions (many-to-many)
- Belongs to many students (saved exams)

### Attempt
- Belongs to student
- Belongs to exam (nullable for quizzes)
- Belongs to subject (nullable for exams)
- Has many attempt questions
- Tracks score, start time, submit time

### AttemptQuestion
- Belongs to attempt
- Belongs to question
- Has many attempt options
- Stores snapshot of question data

### AttemptOption
- Belongs to attempt question
- Belongs to option
- Stores snapshot of option data
- Tracks if option was selected

## Database Schema

The system uses the following main tables:
- `admins` - Administrator accounts
- `students` - Student accounts  
- `subjects` - Subject categories
- `questions` - Question bank
- `options` - Answer options for questions
- `exams` - Exam definitions
- `exam_questions` - Exam-question relationships
- `attempts` - Student attempt records
- `attempt_questions` - Question snapshots in attempts
- `attempt_options` - Option snapshots in attempts
- `exam_savings` - Student saved exams

## Installation

1. Clone the repository
2. Install dependencies: `composer install`
3. Copy `.env.example` to `.env` and configure database
4. Generate application key: `php artisan key:generate`
5. Run migrations: `php artisan migrate`
6. Seed the database: `php artisan db:seed`
7. Start the server: `php artisan serve`

## Authentication

### API Authentication
The API uses Laravel Sanctum for authentication. Include the bearer token in the Authorization header:

```
Authorization: Bearer {token}
```

### Web Authentication
The admin panel uses Laravel's built-in session authentication with a custom 'admin' guard.

## Response Format

All API responses follow a consistent JSON format:

```json
{
    "data": {},
    "message": "Success message",
    "status": "success"
}
```

Error responses include validation errors and appropriate HTTP status codes.

## Recent Updates

### Web Admin Panel Implementation
- Converted admin functionality from pure API to MVC with Bootstrap interfaces
- Created comprehensive admin panel with session-based authentication
- Implemented all CRUD operations with responsive Bootstrap UI
- Added advanced filtering and search capabilities

### Attempt Management System
- Created dedicated attempt management interface
- Added detailed attempt analysis with question-by-question breakdown
- Implemented attempt filtering by type, status, student, exam, and subject
- Added attempt statistics and performance analytics

### Student Dashboard API
- Created comprehensive dashboard API for student applications
- Added attempt history API with filtering and pagination
- Included performance analytics by subject
- Provided real-time exam availability and scheduling information

### Enhanced Features
- CSV export functionality for exam results
- Advanced question creation with dynamic option management
- Bulk question attachment to exams
- Real-time exam status indicators
- Comprehensive statistics and analytics throughout the system
