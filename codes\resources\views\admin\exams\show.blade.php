@extends('admin.layouts.app')

@section('title', 'Exam Details')
@section('page-title', 'Exam Details')

@section('content')
<div class="row">
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clipboard-check"></i>
                    Exam Information
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                        <i class="bi bi-clipboard-check text-white" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="card-title">{{ $exam->title }}</h5>
                    @if($exam->description)
                        <p class="text-muted">{{ $exam->description }}</p>
                    @endif
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Subject</label>
                    <div>
                        <span class="badge bg-info fs-6">{{ $exam->subject->name }}</span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-6">
                        <label class="form-label text-muted">Duration</label>
                        <div>
                            <span class="badge bg-warning fs-6">{{ $exam->duration }} min</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <label class="form-label text-muted">Max Attempts</label>
                        <div>
                            <span class="badge bg-secondary fs-6">{{ $exam->max_attempts }}</span>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Passing Score</label>
                    <div>
                        <span class="badge bg-success fs-6">{{ $exam->passing_score }}%</span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Schedule</label>
                    <div>
                        <small class="text-muted">
                            <i class="bi bi-calendar-event"></i>
                            Start: {{ $exam->start_time->format('M d, Y H:i') }}
                        </small><br>
                        <small class="text-muted">
                            <i class="bi bi-calendar-x"></i>
                            End: {{ $exam->end_time->format('M d, Y H:i') }}
                        </small>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Status</label>
                    <div>
                        @php
                            $now = now();
                            $status = 'upcoming';
                            $statusClass = 'secondary';
                            
                            if ($now->greaterThan($exam->end_time)) {
                                $status = 'ended';
                                $statusClass = 'danger';
                            } elseif ($now->greaterThan($exam->start_time)) {
                                $status = 'active';
                                $statusClass = 'success';
                            }
                        @endphp
                        <span class="badge bg-{{ $statusClass }} fs-6">{{ ucfirst($status) }}</span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Options</label>
                    <div>
                        @if($exam->shuffle_questions)
                            <span class="badge bg-info">Shuffle Questions</span>
                        @endif
                        @if($exam->show_results)
                            <span class="badge bg-success">Show Results</span>
                        @endif
                    </div>
                </div>

                <div class="row text-center mb-3">
                    <div class="col-4">
                        <div class="h5 text-primary">{{ $exam->questions->count() }}</div>
                        <small class="text-muted">Questions</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 text-success">{{ $exam->attempts->count() }}</div>
                        <small class="text-muted">Attempts</small>
                    </div>
                    <div class="col-4">
                        <div class="h5 text-warning">{{ $exam->savedByStudents->count() }}</div>
                        <small class="text-muted">Saved</small>
                    </div>
                </div>

                <hr>

                <div class="text-muted">
                    <small>
                        <i class="bi bi-person"></i>
                        Created by {{ $exam->admin->name }}
                    </small><br>
                    <small>
                        <i class="bi bi-calendar"></i>
                        {{ $exam->created_at->format('M d, Y H:i') }}
                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.exams.index') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to List
                    </a>
                    <a href="{{ route('admin.exams.edit', $exam) }}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Questions -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-question-circle"></i>
                    Attached Questions ({{ $exam->questions->count() }})
                </h6>
                <a href="{{ route('admin.exams.attach-questions-form', $exam) }}" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-plus-lg"></i>
                    Attach Questions
                </a>
            </div>
            <div class="card-body">
                @if($exam->questions->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Question</th>
                                    <th>Type</th>
                                    <th>Options</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($exam->questions as $question)
                                    <tr>
                                        <td>
                                            <div class="text-truncate" style="max-width: 300px;" title="{{ $question->question_text }}">
                                                {{ $question->question_text }}
                                            </div>
                                            @if($question->direction === 'rtl')
                                                <small class="badge bg-secondary">RTL</small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ $question->type === 'mcq' ? 'primary' : 'success' }}">
                                                {{ $question->type === 'mcq' ? 'MCQ' : 'T/F' }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">{{ $question->options->count() }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.questions.show', $question) }}" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Question">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <form method="POST" 
                                                      action="{{ route('admin.exams.detach-question', [$exam, $question]) }}" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to detach this question?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Detach">
                                                        <i class="bi bi-x-lg"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="bi bi-question-circle text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No questions attached yet.</p>
                        <a href="{{ route('admin.exams.attach-questions-form', $exam) }}" class="btn btn-sm btn-primary">
                            <i class="bi bi-plus-lg"></i>
                            Attach First Question
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Recent Attempts -->
        @if($exam->attempts->count() > 0)
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-graph-up"></i>
                        Recent Attempts ({{ $exam->attempts->count() }})
                    </h6>
                    <a href="{{ route('admin.exams.results', $exam) }}" class="btn btn-sm btn-outline-success">
                        <i class="bi bi-graph-up"></i>
                        View All Results
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Student</th>
                                    <th>Score</th>
                                    <th>Started</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($exam->attempts->take(10) as $attempt)
                                    <tr>
                                        <td>
                                            <div class="fw-bold">{{ $attempt->student->name }}</div>
                                            <small class="text-muted">{{ $attempt->student->email }}</small>
                                        </td>
                                        <td>
                                            @if($attempt->score !== null)
                                                <span class="fw-bold text-{{ $attempt->score >= $exam->passing_score ? 'success' : 'danger' }}">
                                                    {{ $attempt->score }}%
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $attempt->started_at->format('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                <span class="badge bg-success">Completed</span>
                                            @else
                                                <span class="badge bg-warning">In Progress</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-lg {
        width: 4rem;
        height: 4rem;
    }
</style>
@endpush
