@extends('admin.layouts.app')

@section('title', 'Attach Questions')
@section('page-title', 'Attach Questions to Exam')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-link-45deg"></i>
                    Attach Questions to: {{ $exam->title }}
                </h5>
                <p class="text-muted mb-0">Subject: {{ $exam->subject->name }}</p>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.exams.attach-questions', $exam) }}">
                    @csrf

                    @if($availableQuestions->count() > 0)
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select-all">
                                <label class="form-check-label fw-bold" for="select-all">
                                    Select All Questions
                                </label>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="header-checkbox" class="form-check-input">
                                        </th>
                                        <th>Question</th>
                                        <th>Type</th>
                                        <th>Options</th>
                                        <th>Created By</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($availableQuestions as $question)
                                        <tr>
                                            <td>
                                                <div class="form-check">
                                                    <input class="form-check-input question-checkbox" 
                                                           type="checkbox" 
                                                           name="question_ids[]" 
                                                           value="{{ $question->id }}"
                                                           id="question-{{ $question->id }}">
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 300px;" title="{{ $question->question_text }}">
                                                    {{ $question->question_text }}
                                                </div>
                                                @if($question->direction === 'rtl')
                                                    <small class="badge bg-secondary">RTL</small>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $question->type === 'mcq' ? 'primary' : 'success' }}">
                                                    {{ $question->type === 'mcq' ? 'Multiple Choice' : 'True/False' }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning">{{ $question->options->count() }}</span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="bi bi-person text-white"></i>
                                                    </div>
                                                    <small>{{ $question->admin->name }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <small>{{ $question->created_at->format('M d, Y') }}</small>
                                            </td>
                                            <td>
                                                <a href="{{ route('admin.questions.show', $question) }}" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Question"
                                                   target="_blank">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $availableQuestions->links() }}
                        </div>

                        @error('question_ids')
                            <div class="alert alert-danger mt-3">{{ $message }}</div>
                        @enderror

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ route('admin.exams.show', $exam) }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i>
                                Back to Exam
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-link-45deg"></i>
                                Attach Selected Questions
                            </button>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="bi bi-question-circle text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">No available questions found for this subject.</p>
                            <p class="text-muted">All questions from this subject are already attached to this exam, or no questions exist.</p>
                            <div class="mt-3">
                                <a href="{{ route('admin.questions.create') }}?subject_id={{ $exam->subject_id }}" class="btn btn-primary me-2">
                                    <i class="bi bi-plus-lg"></i>
                                    Create New Question
                                </a>
                                <a href="{{ route('admin.exams.show', $exam) }}" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left"></i>
                                    Back to Exam
                                </a>
                            </div>
                        </div>
                    @endif
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-sm {
        width: 1.5rem;
        height: 1.5rem;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const headerCheckbox = document.getElementById('header-checkbox');
    const selectAllCheckbox = document.getElementById('select-all');
    const questionCheckboxes = document.querySelectorAll('.question-checkbox');
    
    // Header checkbox functionality
    if (headerCheckbox) {
        headerCheckbox.addEventListener('change', function() {
            questionCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectAllState();
        });
    }
    
    // Select all checkbox functionality
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            questionCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            if (headerCheckbox) {
                headerCheckbox.checked = this.checked;
            }
        });
    }
    
    // Individual checkbox functionality
    questionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateHeaderCheckboxState();
        });
    });
    
    function updateSelectAllState() {
        if (selectAllCheckbox) {
            const checkedCount = document.querySelectorAll('.question-checkbox:checked').length;
            const totalCount = questionCheckboxes.length;
            
            selectAllCheckbox.checked = checkedCount === totalCount;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
        }
    }
    
    function updateHeaderCheckboxState() {
        if (headerCheckbox) {
            const checkedCount = document.querySelectorAll('.question-checkbox:checked').length;
            const totalCount = questionCheckboxes.length;
            
            headerCheckbox.checked = checkedCount === totalCount;
            headerCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
        }
    }
    
    // Initialize states
    updateSelectAllState();
    updateHeaderCheckboxState();
});
</script>
@endpush
