<?php $__env->startSection('title', 'Questions'); ?>
<?php $__env->startSection('page-title', 'Question Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-question-circle"></i>
                    All Questions
                </h6>
                <a href="<?php echo e(route('admin.questions.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i>
                    Add New Question
                </a>
            </div>
            <div class="card-body">
                <?php if($questions->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Question</th>
                                    <th>Subject</th>
                                    <th>Type</th>
                                    <th>Options</th>
                                    <th>Created By</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($question->id); ?></td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 200px;" title="<?php echo e($question->question_text); ?>">
                                                <?php echo e($question->question_text); ?>

                                            </div>
                                            <?php if($question->direction === 'rtl'): ?>
                                                <small class="badge bg-secondary">RTL</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($question->subject->name); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo e($question->type === 'mcq' ? 'primary' : 'success'); ?>">
                                                <?php echo e($question->type === 'mcq' ? 'Multiple Choice' : 'True/False'); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo e($question->options_count ?? 0); ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="bi bi-person text-white"></i>
                                                </div>
                                                <small><?php echo e($question->admin->name); ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <small><?php echo e($question->created_at->format('M d, Y')); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.questions.show', $question)); ?>" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.questions.edit', $question)); ?>" 
                                                   class="btn btn-sm btn-outline-warning" 
                                                   title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <form method="POST" 
                                                      action="<?php echo e(route('admin.questions.destroy', $question)); ?>" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this question?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($questions->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-question-circle text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No questions found.</p>
                        <a href="<?php echo e(route('admin.questions.create')); ?>" class="btn btn-primary">
                            <i class="bi bi-plus-lg"></i>
                            Add First Question
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .avatar-sm {
        width: 1.5rem;
        height: 1.5rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp5\htdocs\eduexamin\codes\resources\views/admin/questions/index.blade.php ENDPATH**/ ?>