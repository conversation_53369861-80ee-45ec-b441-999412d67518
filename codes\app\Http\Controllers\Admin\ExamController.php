<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\Subject;
use App\Models\Question;
use App\Models\Attempt;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ExamController extends Controller
{
    /**
     * Display a listing of exams.
     */
    public function index()
    {
        $exams = Exam::with(['subject', 'admin'])
            ->withCount(['questions', 'attempts'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);
            
        return view('admin.exams.index', compact('exams'));
    }

    /**
     * Show the form for creating a new exam.
     */
    public function create()
    {
        $subjects = Subject::orderBy('name')->get();
        return view('admin.exams.create', compact('subjects'));
    }

    /**
     * Store a newly created exam in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_time' => 'required|date|after:now',
            'end_time' => 'required|date|after:start_time',
            'duration' => 'required|integer|min:1|max:300',
        ]);

        Exam::create([
            'subject_id' => $request->subject_id,
            'title' => $request->title,
            'description' => $request->description,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'duration' => $request->duration,
            'created_by' => auth('admin')->id(),
        ]);

        return redirect()->route('admin.exams.index')
            ->with('success', 'Exam created successfully.');
    }

    /**
     * Display the specified exam.
     */
    public function show(Exam $exam)
    {
        $exam->load(['subject', 'admin', 'questions.options', 'attempts.student']);
        return view('admin.exams.show', compact('exam'));
    }

    /**
     * Show the form for editing the specified exam.
     */
    public function edit(Exam $exam)
    {
        $subjects = Subject::orderBy('name')->get();
        return view('admin.exams.edit', compact('exam', 'subjects'));
    }

    /**
     * Update the specified exam in storage.
     */
    public function update(Request $request, Exam $exam)
    {
        $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'duration' => 'required|integer|min:1|max:300',
        ]);

        $exam->update($request->only([
            'subject_id', 'title', 'description', 
            'start_time', 'end_time', 'duration'
        ]));

        return redirect()->route('admin.exams.index')
            ->with('success', 'Exam updated successfully.');
    }

    /**
     * Remove the specified exam from storage.
     */
    public function destroy(Exam $exam)
    {
        // Check if exam has attempts
        if ($exam->attempts()->count() > 0) {
            return redirect()->route('admin.exams.index')
                ->with('error', 'Cannot delete exam that has student attempts.');
        }

        $exam->delete();

        return redirect()->route('admin.exams.index')
            ->with('success', 'Exam deleted successfully.');
    }

    /**
     * Show form to attach questions to exam.
     */
    public function attachQuestionsForm(Exam $exam)
    {
        $availableQuestions = Question::where('subject_id', $exam->subject_id)
            ->whereNotIn('id', $exam->questions->pluck('id'))
            ->with('options')
            ->get();
            
        return view('admin.exams.attach-questions', compact('exam', 'availableQuestions'));
    }

    /**
     * Attach questions to exam.
     */
    public function attachQuestions(Request $request, Exam $exam)
    {
        $request->validate([
            'question_ids' => 'required|array',
            'question_ids.*' => 'exists:questions,id',
        ]);

        // Verify questions belong to the same subject
        $questions = Question::whereIn('id', $request->question_ids)
            ->where('subject_id', $exam->subject_id)
            ->get();

        if ($questions->count() !== count($request->question_ids)) {
            return back()->with('error', 'Some questions do not belong to the exam subject.');
        }

        $exam->questions()->attach($request->question_ids);

        return redirect()->route('admin.exams.show', $exam)
            ->with('success', 'Questions attached to exam successfully.');
    }

    /**
     * Detach question from exam.
     */
    public function detachQuestion(Exam $exam, Question $question)
    {
        $exam->questions()->detach($question->id);

        return redirect()->route('admin.exams.show', $exam)
            ->with('success', 'Question removed from exam successfully.');
    }

    /**
     * Show exam results.
     */
    public function results(Exam $exam)
    {
        $attempts = Attempt::where('exam_id', $exam->id)
            ->with(['student', 'attemptQuestions.attemptOptions'])
            ->whereNotNull('submitted_at')
            ->orderBy('score', 'desc')
            ->get();

        $stats = [
            'total_attempts' => $attempts->count(),
            'average_score' => $attempts->avg('score') ?? 0,
            'highest_score' => $attempts->max('score') ?? 0,
            'lowest_score' => $attempts->min('score') ?? 0,
        ];

        return view('admin.exams.results', compact('exam', 'attempts', 'stats'));
    }
}
