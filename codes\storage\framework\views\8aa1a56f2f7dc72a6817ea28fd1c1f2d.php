<?php $__env->startSection('title', 'Exams'); ?>
<?php $__env->startSection('page-title', 'Exam Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clipboard-check"></i>
                    All Exams
                </h6>
                <a href="<?php echo e(route('admin.exams.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i>
                    Add New Exam
                </a>
            </div>
            <div class="card-body">
                <?php if($exams->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Title</th>
                                    <th>Subject</th>
                                    <th>Duration</th>
                                    <th>Questions</th>
                                    <th>Attempts</th>
                                    <th>Start Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($exam->id); ?></td>
                                        <td>
                                            <div class="fw-bold"><?php echo e($exam->title); ?></div>
                                            <?php if($exam->description): ?>
                                                <small class="text-muted"><?php echo e(Str::limit($exam->description, 50)); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($exam->subject->name); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo e($exam->duration); ?> min</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo e($exam->questions_count ?? 0); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo e($exam->attempts_count ?? 0); ?></span>
                                        </td>
                                        <td>
                                            <small><?php echo e($exam->start_time->format('M d, Y H:i')); ?></small>
                                        </td>
                                        <td>
                                            <?php
                                                $now = now();
                                                $status = 'upcoming';
                                                $statusClass = 'secondary';
                                                
                                                if ($now->greaterThan($exam->end_time)) {
                                                    $status = 'ended';
                                                    $statusClass = 'danger';
                                                } elseif ($now->greaterThan($exam->start_time)) {
                                                    $status = 'active';
                                                    $statusClass = 'success';
                                                }
                                            ?>
                                            <span class="badge bg-<?php echo e($statusClass); ?>"><?php echo e(ucfirst($status)); ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.exams.show', $exam)); ?>" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.exams.edit', $exam)); ?>" 
                                                   class="btn btn-sm btn-outline-warning" 
                                                   title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <?php if($exam->attempts_count > 0): ?>
                                                    <a href="<?php echo e(route('admin.exams.results', $exam)); ?>" 
                                                       class="btn btn-sm btn-outline-success" 
                                                       title="View Results">
                                                        <i class="bi bi-graph-up"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <form method="POST" 
                                                      action="<?php echo e(route('admin.exams.destroy', $exam)); ?>" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this exam?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($exams->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-clipboard-check text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No exams found.</p>
                        <a href="<?php echo e(route('admin.exams.create')); ?>" class="btn btn-primary">
                            <i class="bi bi-plus-lg"></i>
                            Add First Exam
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp5\htdocs\eduexamin\codes\resources\views/admin/exams/index.blade.php ENDPATH**/ ?>