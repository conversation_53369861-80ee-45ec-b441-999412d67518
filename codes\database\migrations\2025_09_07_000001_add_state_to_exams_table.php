<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('exams', function (Blueprint $table) {
            $table->string('state', 20)->default('draft')->index()->after('duration');
        });

        // Backfill states for existing rows based on time window
        $now = Carbon::now();

        // If times are missing, keep as draft
        DB::table('exams')
            ->whereNull('start_time')
            ->orWhereNull('end_time')
            ->update(['state' => 'draft']);

        // Scheduled: start time in the future
        DB::table('exams')
            ->where('start_time', '>', $now)
            ->update(['state' => 'scheduled']);

        // Active: now within [start, end]
        DB::table('exams')
            ->where('start_time', '<=', $now)
            ->where('end_time', '>=', $now)
            ->update(['state' => 'active']);

        // Closed: end time has passed
        DB::table('exams')
            ->where('end_time', '<', $now)
            ->update(['state' => 'closed']);
    }

    public function down(): void
    {
        Schema::table('exams', function (Blueprint $table) {
            $table->dropColumn('state');
        });
    }
};
