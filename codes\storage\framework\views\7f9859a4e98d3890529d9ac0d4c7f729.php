<?php $__env->startSection('title', 'Student Attempts'); ?>
<?php $__env->startSection('page-title', 'Student Attempts'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-circle"></i>
                    Student Information
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="avatar-lg bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                    <i class="bi bi-person text-white" style="font-size: 2rem;"></i>
                </div>
                <h5 class="card-title"><?php echo e($student->name); ?></h5>
                <p class="text-muted"><?php echo e($student->email); ?></p>
                
                <?php if($student->email_verified_at): ?>
                    <span class="badge bg-success mb-3">Email Verified</span>
                <?php else: ?>
                    <span class="badge bg-warning mb-3">Email Not Verified</span>
                <?php endif; ?>

                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="h4 text-primary"><?php echo e($statistics['total_attempts']); ?></div>
                        <small class="text-muted">Total Attempts</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success"><?php echo e($statistics['completed_attempts']); ?></div>
                        <small class="text-muted">Completed</small>
                    </div>
                </div>

                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="h5 text-info"><?php echo e($statistics['exam_attempts']); ?></div>
                        <small class="text-muted">Exams</small>
                    </div>
                    <div class="col-6">
                        <div class="h5 text-warning"><?php echo e($statistics['quiz_attempts']); ?></div>
                        <small class="text-muted">Quizzes</small>
                    </div>
                </div>

                <?php if($statistics['completed_attempts'] > 0): ?>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="h6 text-success"><?php echo e($statistics['average_score']); ?>%</div>
                            <small class="text-muted">Average Score</small>
                        </div>
                        <div class="col-6">
                            <div class="h6 text-primary"><?php echo e($statistics['highest_score']); ?>%</div>
                            <small class="text-muted">Highest Score</small>
                        </div>
                    </div>
                <?php endif; ?>

                <hr>

                <div class="text-muted">
                    <small>
                        <i class="bi bi-calendar"></i>
                        Joined <?php echo e($student->created_at->format('M d, Y')); ?>

                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="<?php echo e(route('admin.students.index')); ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Students
                    </a>
                    <a href="<?php echo e(route('admin.students.show', $student)); ?>" class="btn btn-primary">
                        <i class="bi bi-eye"></i>
                        View Profile
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clipboard-data"></i>
                    Attempt History (<?php echo e($attempts->total()); ?>)
                </h6>
            </div>
            <div class="card-body">
                <?php if($attempts->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Type</th>
                                    <th>Exam/Subject</th>
                                    <th>Score</th>
                                    <th>Started</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $attempts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attempt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($attempt->id); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo e($attempt->type === 'exam' ? 'primary' : 'info'); ?>">
                                                <?php echo e(ucfirst($attempt->type)); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php if($attempt->type === 'exam' && $attempt->exam): ?>
                                                <div class="fw-bold"><?php echo e($attempt->exam->title); ?></div>
                                                <small class="text-muted"><?php echo e($attempt->exam->subject->name); ?></small>
                                            <?php elseif($attempt->type === 'quiz' && $attempt->subject): ?>
                                                <div class="fw-bold"><?php echo e($attempt->subject->name); ?></div>
                                                <small class="text-muted">Quiz</small>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($attempt->score !== null): ?>
                                                <span class="fw-bold text-<?php echo e($attempt->score >= 60 ? 'success' : 'danger'); ?>">
                                                    <?php echo e($attempt->score); ?>%
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo e($attempt->started_at->format('M d, Y H:i')); ?></small>
                                        </td>
                                        <td>
                                            <?php if($attempt->submitted_at): ?>
                                                <?php
                                                    $duration = $attempt->started_at->diffInMinutes($attempt->submitted_at);
                                                ?>
                                                <small><?php echo e($duration); ?> min</small>
                                            <?php else: ?>
                                                <?php
                                                    $duration = $attempt->started_at->diffInMinutes(now());
                                                ?>
                                                <small class="text-warning"><?php echo e($duration); ?> min (ongoing)</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($attempt->submitted_at): ?>
                                                <span class="badge bg-success">Completed</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">In Progress</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.attempts.show', $attempt)); ?>" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <form method="POST" 
                                                      action="<?php echo e(route('admin.attempts.destroy', $attempt)); ?>" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this attempt?')">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($attempts->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-clipboard-data text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No attempts found for this student.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .avatar-lg {
        width: 4rem;
        height: 4rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp5\htdocs\eduexamin\codes\resources\views/admin/attempts/student.blade.php ENDPATH**/ ?>