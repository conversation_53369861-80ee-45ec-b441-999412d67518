<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\Question;
use App\Models\ExamSaving;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ExamController extends Controller
{
    public function index()
    {
        return response()->json(
            Exam::with(['subject', 'admin'])
                // Removed: ->withCount('registrations')
                ->paginate(15)
        );
    }

    public function store(Request $request)
    {
        $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_time' => 'required|date|after:now',
            'end_time' => 'required|date|after:start_time',
            'duration' => 'required|integer|min:1',
            'state' => 'nullable|in:draft,scheduled,active,closed',
            'question_ids' => 'array',
            'question_ids.*' => 'exists:questions,id',
        ]);

        $exam = Exam::create([
            'subject_id' => $request->subject_id,
            'title' => $request->title,
            'description' => $request->description,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
            'duration' => $request->duration,
            'state' => $request->state ?? 'draft',
            'created_by' => auth()->id(),
        ]);

        if ($request->question_ids) {
            $exam->questions()->attach($request->question_ids);
        }

        return response()->json($exam->load(['questions', 'subject']), 201);
    }

    public function show(Exam $exam)
    {
        return response()->json($exam->load(['subject', 'questions.options', 'admin']));
    }

    public function update(Request $request, Exam $exam)
    {
        $request->validate([
            'subject_id' => 'exists:subjects,id',
            'title' => 'string|max:255',
            'description' => 'nullable|string',
            'start_time' => 'date',
            'end_time' => 'date|after:start_time',
            'duration' => 'integer|min:1',
            'state' => 'nullable|in:draft,scheduled,active,closed',
        ]);

        $exam->update($request->only([
            'subject_id', 'title', 'description', 
            'start_time', 'end_time', 'duration', 'state'
        ]));

        return response()->json($exam->load(['questions', 'subject']));
    }

    public function destroy(Exam $exam)
    {
        $exam->delete();
        return response()->json(null, 204);
    }

    public function available()
    {
        $now = Carbon::now();
        return response()->json(
            Exam::with([
                'subject',
                // Removed: 'registrations' => function ($q) { $q->where('student_id', auth()->id()); },
            ])
                ->where(function ($q) use ($now) {
                    $q->where('state', 'active')
                      ->orWhere(function ($q2) use ($now) {
                        $q2->where('start_time', '<=', $now)
                           ->where('end_time', '>=', $now);
                      });
                })
                ->get()
        );
    }

    public function attachQuestions(Request $request, Exam $exam)
    {
        $request->validate([
            'question_ids' => 'required|array',
            'question_ids.*' => 'exists:questions,id',
        ]);

        $exam->questions()->syncWithoutDetaching($request->question_ids);

        return response()->json($exam->load('questions'));
    }

    public function detachQuestion(Exam $exam, Question $question)
    {
        $exam->questions()->detach($question->id);

        return response()->json(['message' => 'Question removed from exam']);
    }

    public function results(Exam $exam)
    {
        return response()->json([
            'exam' => $exam->load('subject'),
            'attempts' => $exam->attempts()
                ->with(['student'])
                ->whereNotNull('submitted_at')
                ->get(),
            'statistics' => [
                'total_attempts' => $exam->attempts()->count(),
                'completed_attempts' => $exam->attempts()->whereNotNull('submitted_at')->count(),
                'average_score' => $exam->attempts()->whereNotNull('score')->avg('score'),
                // Removed: 'registrations' => $exam->registrations()->count(),
            ]
        ]);
    }

    // Student save endpoints (under student middleware/routes)
    public function save(Exam $exam)
    {
        $studentId = auth()->id();
        $exists = ExamSaving::where('exam_id', $exam->id)
            ->where('student_id', $studentId)
            ->exists();
        if ($exists) {
            return response()->json(['message' => 'Already saved'], 200);
        }

        ExamSaving::create([
            'exam_id' => $exam->id,
            'student_id' => $studentId,
        ]);

        return response()->json(['message' => 'Saved successfully'], 201);
    }

    public function unsave(Exam $exam)
    {
        $studentId = auth()->id();
        ExamSaving::where('exam_id', $exam->id)
            ->where('student_id', $studentId)
            ->delete();
        return response()->json(['message' => 'Unsaved successfully']);
    }

    public function incomingExams(Request $request)
    {
        $student = $request->user();
        $now = Carbon::now();
        $exams = Exam::where('start_time', '>', $now)
            ->get();
        return response()->json($exams);
    }

  public function savedExams(Request $request)
    {
        $student = $request->user();

        $exams = $student->savedExams()
            ->with(['subject', 'admin']) // eager load to avoid N+1 queries
            ->get();

        return response()->json($exams);
    }

    public function startExam(Request $request, $examId)
    {
        $student = $request->user();
        $now = Carbon::now();
        $exam = Exam::where('id', $examId)
            ->where('start_time', '<=', $now)
            ->where('end_time', '>=', $now)
            ->first();
        if (!$exam) {
            return response()->json(['error' => 'Exam not available to start'], 403);
        }
        // Logic to start the exam goes here
        return response()->json(['message' => 'Exam started', 'exam' => $exam]);
    }
}
