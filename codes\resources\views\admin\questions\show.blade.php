@extends('admin.layouts.app')

@section('title', 'Question Details')
@section('page-title', 'Question Details')

@section('content')
<div class="row">
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    Question Information
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label text-muted">Subject</label>
                    <div>
                        <span class="badge bg-info fs-6">{{ $question->subject->name }}</span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Type</label>
                    <div>
                        <span class="badge bg-{{ $question->type === 'mcq' ? 'primary' : 'success' }} fs-6">
                            {{ $question->type === 'mcq' ? 'Multiple Choice' : 'True/False' }}
                        </span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Text Direction</label>
                    <div>
                        <span class="badge bg-secondary fs-6">{{ strtoupper($question->direction) }}</span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Created By</label>
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                            <i class="bi bi-person text-white"></i>
                        </div>
                        <div>
                            <div class="fw-bold">{{ $question->admin->name }}</div>
                            <small class="text-muted">{{ $question->admin->email }}</small>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Statistics</label>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="h5 text-primary">{{ $question->options->count() }}</div>
                            <small class="text-muted">Options</small>
                        </div>
                        <div class="col-6">
                            <div class="h5 text-success">{{ $question->exams->count() }}</div>
                            <small class="text-muted">Used in Exams</small>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="text-muted">
                    <small>
                        <i class="bi bi-calendar"></i>
                        Created {{ $question->created_at->format('M d, Y H:i') }}
                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.questions.index') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to List
                    </a>
                    <a href="{{ route('admin.questions.edit', $question) }}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Question Text -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-question-circle-fill"></i>
                    Question Text
                </h5>
            </div>
            <div class="card-body">
                <div class="p-3 bg-light rounded" dir="{{ $question->direction }}">
                    <p class="mb-0 fs-5">{{ $question->question_text }}</p>
                </div>
            </div>
        </div>

        <!-- Answer Options -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-check"></i>
                    Answer Options ({{ $question->options->count() }})
                </h5>
            </div>
            <div class="card-body">
                @if($question->options->count() > 0)
                    <div class="row">
                        @foreach($question->options as $index => $option)
                            <div class="col-md-6 mb-3">
                                <div class="card {{ $option->is_correct ? 'border-success' : 'border-light' }}">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <span class="badge bg-secondary">Option {{ $index + 1 }}</span>
                                            @if($option->is_correct)
                                                <span class="badge bg-success">
                                                    <i class="bi bi-check-lg"></i> Correct
                                                </span>
                                            @endif
                                        </div>
                                        <div dir="{{ $option->direction }}">
                                            <p class="mb-0">{{ $option->option_text }}</p>
                                        </div>
                                        @if($option->direction === 'rtl')
                                            <small class="text-muted">RTL</small>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-3">
                        <i class="bi bi-list-check text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No options found.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Used in Exams -->
        @if($question->exams->count() > 0)
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clipboard-check"></i>
                        Used in Exams ({{ $question->exams->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Exam Title</th>
                                    <th>Subject</th>
                                    <th>Duration</th>
                                    <th>Start Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($question->exams as $exam)
                                    <tr>
                                        <td>
                                            <div class="fw-bold">{{ $exam->title }}</div>
                                            @if($exam->description)
                                                <small class="text-muted">{{ Str::limit($exam->description, 50) }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $exam->subject->name }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">{{ $exam->duration }} min</span>
                                        </td>
                                        <td>
                                            <small>{{ $exam->start_time->format('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.exams.show', $exam) }}" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="View Exam">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-sm {
        width: 2rem;
        height: 2rem;
    }
</style>
@endpush
