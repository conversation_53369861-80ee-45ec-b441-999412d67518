# Repository Overview

- **Framework**: Laravel PHP application located under `c:\xampp5\htdocs\eduexamin\codes`.
- **Primary Entry**: Routes defined in `routes/api.php` and `routes/web.php` inside the `codes` subdirectory.
- **Namespace Pattern**: API controllers reside in `App\Http\Controllers\Api`.

# API Routes Summary (`routes/api.php`)

## Authentication (`/auth` prefix)
1. `POST /auth/admin/login` → `AuthController@adminLogin`
2. `POST /auth/student/login` → `AuthController@studentLogin`
3. `POST /auth/student/register` → `AuthController@studentRegister`
4. `POST /auth/student/verify-code` → `AuthController@verifyCode`
5. `POST /auth/logout` → `AuthController@logout` (requires `auth:sanctum`)
6. `GET /auth/me` → `AuthController@me` (requires `auth:sanctum`)

## Admin (`/admin` prefix, middleware `auth:sanctum`, `role:admin`)
1. `Route::apiResource('admins', AdminController)`
2. `Route::apiResource('students', StudentController)`
3. `Route::apiResource('subjects', SubjectController)`
4. `Route::apiResource('questions', QuestionController)`
5. `Route::apiResource('exams', ExamController)`
6. `POST /admin/exams/{exam}/questions` → `ExamController@attachQuestions`
7. `DELETE /admin/exams/{exam}/questions/{question}` → `ExamController@detachQuestion`
8. `GET /admin/analytics/dashboard` → `AdminController@dashboard`
9. `GET /admin/analytics/exam/{exam}/results` → `ExamController@results`
10. `PUT /admin/password` → `AuthController@updatePassword`

## Student (`/student` prefix, middleware `auth:sanctum`, `role:student`)
1. `GET /student/subjects` → `SubjectController@index`
2. `GET /student/exams/available` → `ExamController@available`
3. `GET /student/exams/icoming` → `ExamController@incomingExams`
4. `GET /student/exams/saved` → `ExamController@savedExams`
5. `GET /student/exams/{exam}` → `ExamController@show`
6. `POST /student/exams/{exam}/attempt` → `AttemptController@startExam`
7. `POST /student/subjects/{subject}/quiz` → `AttemptController@startQuiz`
8. `GET /student/attempts/{attempt}` → `AttemptController@show`
9. `POST /student/attempts/{attempt}/submit` → `AttemptController@submit`
10. `POST /student/attempts/{attempt}/answer` → `AttemptController@saveAnswer`
11. `GET /student/my-attempts` → `AttemptController@myAttempts`
12. `POST /student/exams/{exam}/save` → `ExamController@save`
13. `DELETE /student/exams/{exam}/unsave` → `ExamController@unsave`
14. `GET /student/password` → `AuthController@updatePassword`

# Key Models

## `App\Models\Admin`
- **Extends**: `Illuminate\Foundation\Auth\User` (Authenticatable)
- **Traits**: `HasFactory`, `Notifiable`, `HasApiTokens`, `HasRoles`
- **Fillable**: `name`, `email`, `password`
- **Hidden**: `password`, `remember_token`
- **Casts**: `password` hashed
- **Relations**:
  - `questions()` → `hasMany` `Question` (foreign key `created_by`)
  - `exams()` → `hasMany` `Exam` (foreign key `created_by`)

# Current Admin API Controller (`App\Http\Controllers\Api\AdminController`)

- **index()**: Returns paginated admins as JSON.
- **store()**: Validates and creates an admin, assigns `admin` role, returns JSON.
- **show()**: Returns a single admin with eager-loaded `questions` and `exams` as JSON.
- **update()**: Validates, updates name/email, returns JSON.
- **destroy()**: Deletes admin, returns HTTP 204.
- **dashboard()**: Returns counts of subjects, questions, students, exams, attempts, and recent attempts with relations as JSON.

# Immediate Next Steps Requested by User

- Convert admin functionality from pure API to MVC web routes.
- Build Bootstrap-based UI pages for admin interactions.
- Ensure documentation evolves alongside route/controller changes.