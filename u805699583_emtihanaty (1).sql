-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- مضيف: 127.0.0.1:3306
-- وقت الجيل: 02 أكتوبر 2025 الساعة 21:40
-- إصد<PERSON>ر الخادم: 11.8.3-MariaDB-log
-- نسخة PHP: 7.2.34

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- ق<PERSON><PERSON><PERSON>ة بيانات: `u805699583_emtihanaty`
--

-- --------------------------------------------------------


--
-- بنية الجدول `subjects`
--

CREATE TABLE `subjects` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `subjects`
--

INSERT INTO `subjects` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES
(1, 'Java', 'Introduction to Java programming basics', '2025-09-12 20:19:59', '2025-09-12 20:19:59');

-- --------------------------------------------------------

--
-- بنية الجدول `questions`
--

CREATE TABLE `questions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `subject_id` bigint(20) UNSIGNED NOT NULL,
  `question_text` text NOT NULL,
  `direction` enum('ltr','rtl') NOT NULL DEFAULT 'ltr',
  `type` enum('mcq','true_false') NOT NULL,
  `created_by` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `questions`
--

INSERT INTO `questions` (`id`, `subject_id`, `question_text`, `direction`, `type`, `created_by`, `created_at`, `updated_at`) VALUES
(613 - 612, 1, 'Java was developed by which company?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(614 - 612, 1, 'Java is platform-independent because of?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(615 - 612, 1, 'Which keyword is used to define a class in Java?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(616 - 612, 1, 'Which method is the entry point of every Java application?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(617 - 612, 1, 'Java supports multiple inheritance through?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(618 - 612, 1, 'Which of these is NOT a Java primitive type?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(619 - 612, 1, 'Which package is imported by default in Java?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(620 - 612, 1, 'Which symbol is used to end a Java statement?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(621 - 612, 1, 'Which keyword is used to inherit a class in Java?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(622 - 612, 1, 'Which OOP concept is achieved by method overloading in Java?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(623 - 612, 1, 'Which modifier makes a variable accessible only within its class?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(624 - 612, 1, 'Which collection class does not allow duplicate elements?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(625 - 612, 1, 'Which keyword is used to prevent method overriding?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(626 - 612, 1, 'Which of the following is used for garbage collection in Java?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(627 - 612, 1, 'Which operator is used for string concatenation in Java?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(628 - 612, 1, 'Which method is called automatically when an object is created?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(629 - 612, 1, 'Which keyword is used to create an object in Java?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(630 - 612, 1, 'What is the size of int data type in Java?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(631 - 612, 1, 'Which of these is a reserved keyword in Java?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(632 - 612, 1, 'Which access modifier allows visibility in all classes?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(633 - 612, 1, 'Which of these keywords is used for exception handling?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(634 - 612, 1, 'Which keyword is used to create an interface in Java?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(635 - 612, 1, 'Which loop is guaranteed to execute at least once?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(636 - 612, 1, 'Which data type can store Unicode characters?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(637 - 612, 1, 'Which exception is thrown when dividing by zero in Java?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(638 - 612, 1, 'Which method is used to get the length of a string?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(639 - 612, 1, 'Which Java version introduced lambda expressions?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(640 - 612, 1, 'Which keyword is used to define constants in Java?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(641 - 612, 1, 'Which method is the main starting point of Java threads?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(642 - 612, 1, 'Which file extension is used for compiled Java bytecode?', 'ltr', 'mcq', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59');

-- --------------------------------------------------------


--
-- بنية الجدول `options`
--

CREATE TABLE `options` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `question_id` bigint(20) UNSIGNED NOT NULL,
  `option_text` varchar(255) NOT NULL,
  `direction` enum('ltr','rtl') NOT NULL DEFAULT 'ltr',
  `is_correct` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `options`
--

INSERT INTO `options` (`id`, `question_id`, `option_text`, `direction`, `is_correct`, `created_at`, `updated_at`) VALUES
(53 - 52, 613 - 612, 'Sun Microsystems', 'ltr', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(54 - 52, 613 - 612, 'Microsoft', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(55 - 52, 613 - 612, 'Google', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(56 - 52, 613 - 612, 'Apple', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(57 - 52, 614 - 612, 'JVM', 'ltr', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(58 - 52, 614 - 612, 'Compiler', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(59 - 52, 614 - 612, 'Assembler', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(60 - 52, 614 - 612, 'BIOS', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(61 - 52, 615 - 612, 'class', 'ltr', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(62 - 52, 615 - 612, 'struct', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(63 - 52, 615 - 612, 'define', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(64 - 52, 615 - 612, 'package', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(65 - 52, 616 - 612, 'main()', 'ltr', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(66 - 52, 616 - 612, 'start()', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(67 - 52, 616 - 612, 'init()', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(68 - 52, 616 - 612, 'execute()', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(69 - 52, 617 - 612, 'Interfaces', 'ltr', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(70 - 52, 617 - 612, 'Abstract classes', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(71 - 52, 617 - 612, 'Packages', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(72 - 52, 617 - 612, 'Objects', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(73 - 52, 618 - 612, 'String', 'ltr', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(74 - 52, 618 - 612, 'int', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(75 - 52, 618 - 612, 'boolean', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(76 - 52, 618 - 612, 'char', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(77 - 52, 619 - 612, 'java.lang', 'ltr', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(78 - 52, 619 - 612, 'java.util', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(79 - 52, 619 - 612, 'java.io', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(80 - 52, 619 - 612, 'java.sql', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(81 - 52, 620 - 612, ';', 'ltr', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(82 - 52, 620 - 612, ':', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(83 - 52, 620 - 612, '.', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(84 - 52, 620 - 612, ',', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(85 - 52, 621 - 612, 'extends', 'ltr', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(86 - 52, 621 - 612, 'implements', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(87 - 52, 621 - 612, 'inherits', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(88 - 52, 621 - 612, 'super', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(89 - 52, 622 - 612, 'Polymorphism', 'ltr', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(90 - 52, 622 - 612, 'Inheritance', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(91 - 52, 622 - 612, 'Abstraction', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(92 - 52, 622 - 612, 'Encapsulation', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(93 - 52, 623 - 612, 'private', 'ltr', 1, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(94 - 52, 623 - 612, 'protected', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(95 - 52, 623 - 612, 'public', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(96 - 52, 623 - 612, 'default', 'ltr', 0, '2025-09-12 20:19:59', '2025-09-12 20:19:59'),
(97 - 52, 624 - 612, 'Set', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(98 - 52, 624 - 612, 'List', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(99 - 52, 624 - 612, 'Queue', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(100 - 52, 624 - 612, 'Map', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(101 - 52, 625 - 612, 'final', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(102 - 52, 625 - 612, 'static', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(103 - 52, 625 - 612, 'private', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(104 - 52, 625 - 612, 'abstract', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(105 - 52, 626 - 612, 'Automatic memory management', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(106 - 52, 626 - 612, 'delete keyword', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(107 - 52, 626 - 612, 'free() function', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(108 - 52, 626 - 612, 'Destructor', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(109 - 52, 627 - 612, '+', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(110 - 52, 627 - 612, '&', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(111 - 52, 627 - 612, 'concat()', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(112 - 52, 627 - 612, '*', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(113 - 52, 628 - 612, 'Constructor', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(114 - 52, 628 - 612, 'main()', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(115 - 52, 628 - 612, 'finalize()', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(116 - 52, 628 - 612, 'init()', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(117 - 52, 629 - 612, 'new', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(118 - 52, 629 - 612, 'create', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(119 - 52, 629 - 612, 'make', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(120 - 52, 629 - 612, 'construct', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(121 - 52, 630 - 612, '4 bytes', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(122 - 52, 630 - 612, '2 bytes', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(123 - 52, 630 - 612, '8 bytes', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(124 - 52, 630 - 612, '16 bytes', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(125 - 52, 631 - 612, 'volatile', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(126 - 52, 631 - 612, 'main', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(127 - 52, 631 - 612, 'String', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(128 - 52, 631 - 612, 'function', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(129 - 52, 632 - 612, 'public', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(130 - 52, 632 - 612, 'protected', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(131 - 52, 632 - 612, 'private', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(132 - 52, 632 - 612, 'package', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(133 - 52, 633 - 612, 'try-catch', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(134 - 52, 633 - 612, 'exception', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(135 - 52, 633 - 612, 'throwable', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(136 - 52, 633 - 612, 'handle', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(137 - 52, 634 - 612, 'interface', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(138 - 52, 634 - 612, 'abstract', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(139 - 52, 634 - 612, 'module', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(140 - 52, 634 - 612, 'struct', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(141 - 52, 635 - 612, 'do-while', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(142 - 52, 635 - 612, 'for', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(143 - 52, 635 - 612, 'while', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(144 - 52, 635 - 612, 'foreach', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(145 - 52, 636 - 612, 'char', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(146 - 52, 636 - 612, 'String', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(147 - 52, 636 - 612, 'byte', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(148 - 52, 636 - 612, 'int', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(149 - 52, 637 - 612, 'ArithmeticException', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(150 - 52, 637 - 612, 'NullPointerException', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(151 - 52, 637 - 612, 'IOException', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(152 - 52, 637 - 612, 'IndexOutOfBoundsException', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(153 - 52, 638 - 612, 'length()', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(154 - 52, 638 - 612, 'size()', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(155 - 52, 638 - 612, 'count()', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(156 - 52, 638 - 612, 'len()', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(157 - 52, 639 - 612, 'Java 8', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(158 - 52, 639 - 612, 'Java 6', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(159 - 52, 639 - 612, 'Java 7', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(160 - 52, 639 - 612, 'Java 9', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(161 - 52, 640 - 612, 'final', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(162 - 52, 640 - 612, 'const', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(163 - 52, 640 - 612, 'static', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(164 - 52, 640 - 612, 'define', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(165 - 52, 641 - 612, 'run()', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(166 - 52, 641 - 612, 'start()', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(167 - 52, 641 - 612, 'execute()', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(168 - 52, 641 - 612, 'main()', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(169 - 52, 642 - 612, '.class', 'ltr', 1, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(170 - 52, 642 - 612, '.java', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(171 - 52, 642 - 612, '.exe', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00'),
(172 - 52, 642 - 612, '.jar', 'ltr', 0, '2025-09-12 20:20:00', '2025-09-12 20:20:00');

--
-- Indexes for dumped tables
--

--
-- فهارس للجدول `options`
--
ALTER TABLE `options`
  ADD PRIMARY KEY (`id`),
  ADD KEY `options_question_id_foreign` (`question_id`);

--
-- فهارس للجدول `questions`
--
ALTER TABLE `questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `questions_subject_id_foreign` (`subject_id`),
  ADD KEY `questions_created_by_foreign` (`created_by`);

--
-- فهارس للجدول `subjects`
--
ALTER TABLE `subjects`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `options`
--
ALTER TABLE `options`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=173;

--
-- AUTO_INCREMENT for table `questions`
--
ALTER TABLE `questions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=643;

--
-- AUTO_INCREMENT for table `subjects`
--
ALTER TABLE `subjects`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- القيود المفروضة على الجداول الملقاة
--

--
-- قيود الجداول `options`
--
ALTER TABLE `options`
  ADD CONSTRAINT `options_question_id_foreign` FOREIGN KEY (`question_id`) REFERENCES `questions` (`id`) ON DELETE CASCADE;

--
-- قيود الجداول `questions`
--
ALTER TABLE `questions`
  ADD CONSTRAINT `questions_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `questions_subject_id_foreign` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
