<?php $__env->startSection('title', 'Admin Details'); ?>
<?php $__env->startSection('page-title', 'Admin Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-circle"></i>
                    Admin Information
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="avatar-lg bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                    <i class="bi bi-person text-white" style="font-size: 2rem;"></i>
                </div>
                <h5 class="card-title"><?php echo e($admin->name); ?></h5>
                <p class="text-muted"><?php echo e($admin->email); ?></p>
                
                <?php if($admin->id === auth('admin')->id()): ?>
                    <span class="badge bg-primary mb-3">Current User</span>
                <?php endif; ?>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 text-primary"><?php echo e($admin->questions->count()); ?></div>
                        <small class="text-muted">Questions Created</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success"><?php echo e($admin->exams->count()); ?></div>
                        <small class="text-muted">Exams Created</small>
                    </div>
                </div>

                <hr>

                <div class="text-muted">
                    <small>
                        <i class="bi bi-calendar"></i>
                        Joined <?php echo e($admin->created_at->format('M d, Y')); ?>

                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="<?php echo e(route('admin.admins.index')); ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to List
                    </a>
                    <a href="<?php echo e(route('admin.admins.edit', $admin)); ?>" class="btn btn-primary">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Questions Created -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-question-circle"></i>
                    Questions Created (<?php echo e($admin->questions->count()); ?>)
                </h6>
                <a href="<?php echo e(route('admin.questions.create')); ?>" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-plus-lg"></i>
                    Add Question
                </a>
            </div>
            <div class="card-body">
                <?php if($admin->questions->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Question</th>
                                    <th>Subject</th>
                                    <th>Type</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $admin->questions->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="text-truncate" style="max-width: 200px;">
                                                <?php echo e($question->question_text); ?>

                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($question->subject->name); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo e(ucfirst($question->type)); ?></span>
                                        </td>
                                        <td>
                                            <small><?php echo e($question->created_at->format('M d, Y')); ?></small>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if($admin->questions->count() > 5): ?>
                        <div class="text-center">
                            <a href="<?php echo e(route('admin.questions.index')); ?>" class="btn btn-sm btn-outline-primary">
                                View All Questions
                            </a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="bi bi-question-circle text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No questions created yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Exams Created -->
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clipboard-check"></i>
                    Exams Created (<?php echo e($admin->exams->count()); ?>)
                </h6>
                <a href="<?php echo e(route('admin.exams.create')); ?>" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-plus-lg"></i>
                    Add Exam
                </a>
            </div>
            <div class="card-body">
                <?php if($admin->exams->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Title</th>
                                    <th>Subject</th>
                                    <th>Duration</th>
                                    <th>Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $admin->exams->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?php echo e($exam->title); ?></div>
                                            <?php if($exam->description): ?>
                                                <small class="text-muted"><?php echo e(Str::limit($exam->description, 50)); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($exam->subject->name); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo e($exam->duration); ?> min</span>
                                        </td>
                                        <td>
                                            <small><?php echo e($exam->created_at->format('M d, Y')); ?></small>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if($admin->exams->count() > 5): ?>
                        <div class="text-center">
                            <a href="<?php echo e(route('admin.exams.index')); ?>" class="btn btn-sm btn-outline-primary">
                                View All Exams
                            </a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="bi bi-clipboard-check text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No exams created yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .avatar-lg {
        width: 4rem;
        height: 4rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp5\htdocs\eduexamin\codes\resources\views/admin/admins/show.blade.php ENDPATH**/ ?>