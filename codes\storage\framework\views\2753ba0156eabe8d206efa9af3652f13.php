<?php $__env->startSection('title', 'Subject Details'); ?>
<?php $__env->startSection('page-title', 'Subject Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-book-half"></i>
                    Subject Information
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="avatar-lg bg-success rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                    <i class="bi bi-book text-white" style="font-size: 2rem;"></i>
                </div>
                <h5 class="card-title"><?php echo e($subject->name); ?></h5>
                
                <?php if($subject->description): ?>
                    <p class="text-muted"><?php echo e($subject->description); ?></p>
                <?php else: ?>
                    <p class="text-muted">No description provided</p>
                <?php endif; ?>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 text-primary"><?php echo e($subject->questions->count()); ?></div>
                        <small class="text-muted">Questions</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success"><?php echo e($subject->exams->count()); ?></div>
                        <small class="text-muted">Exams</small>
                    </div>
                </div>

                <hr>

                <div class="text-muted">
                    <small>
                        <i class="bi bi-calendar"></i>
                        Created <?php echo e($subject->created_at->format('M d, Y')); ?>

                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="<?php echo e(route('admin.subjects.index')); ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to List
                    </a>
                    <a href="<?php echo e(route('admin.subjects.edit', $subject)); ?>" class="btn btn-primary">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Questions -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-question-circle"></i>
                    Questions (<?php echo e($subject->questions->count()); ?>)
                </h6>
                <a href="<?php echo e(route('admin.questions.create')); ?>?subject_id=<?php echo e($subject->id); ?>" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-plus-lg"></i>
                    Add Question
                </a>
            </div>
            <div class="card-body">
                <?php if($subject->questions->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Question</th>
                                    <th>Type</th>
                                    <th>Created By</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $subject->questions->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="text-truncate" style="max-width: 250px;">
                                                <?php echo e($question->question_text); ?>

                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo e(ucfirst($question->type)); ?></span>
                                        </td>
                                        <td>
                                            <small><?php echo e($question->admin->name); ?></small>
                                        </td>
                                        <td>
                                            <small><?php echo e($question->created_at->format('M d, Y')); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.questions.show', $question)); ?>" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.questions.edit', $question)); ?>" 
                                                   class="btn btn-sm btn-outline-warning" 
                                                   title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if($subject->questions->count() > 10): ?>
                        <div class="text-center">
                            <a href="<?php echo e(route('admin.subjects.questions', $subject)); ?>" class="btn btn-sm btn-outline-primary">
                                View All Questions
                            </a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="bi bi-question-circle text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No questions created yet.</p>
                        <a href="<?php echo e(route('admin.questions.create')); ?>?subject_id=<?php echo e($subject->id); ?>" class="btn btn-sm btn-primary">
                            <i class="bi bi-plus-lg"></i>
                            Create First Question
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Exams -->
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clipboard-check"></i>
                    Exams (<?php echo e($subject->exams->count()); ?>)
                </h6>
                <a href="<?php echo e(route('admin.exams.create')); ?>?subject_id=<?php echo e($subject->id); ?>" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-plus-lg"></i>
                    Add Exam
                </a>
            </div>
            <div class="card-body">
                <?php if($subject->exams->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Title</th>
                                    <th>Duration</th>
                                    <th>Created By</th>
                                    <th>Start Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $subject->exams->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?php echo e($exam->title); ?></div>
                                            <?php if($exam->description): ?>
                                                <small class="text-muted"><?php echo e(Str::limit($exam->description, 50)); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo e($exam->duration); ?> min</span>
                                        </td>
                                        <td>
                                            <small><?php echo e($exam->admin->name); ?></small>
                                        </td>
                                        <td>
                                            <small><?php echo e($exam->start_time->format('M d, Y H:i')); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.exams.show', $exam)); ?>" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.exams.edit', $exam)); ?>" 
                                                   class="btn btn-sm btn-outline-warning" 
                                                   title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <?php if($subject->exams->count() > 10): ?>
                        <div class="text-center">
                            <a href="<?php echo e(route('admin.exams.index')); ?>?subject_id=<?php echo e($subject->id); ?>" class="btn btn-sm btn-outline-primary">
                                View All Exams
                            </a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="bi bi-clipboard-check text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No exams created yet.</p>
                        <a href="<?php echo e(route('admin.exams.create')); ?>?subject_id=<?php echo e($subject->id); ?>" class="btn btn-sm btn-primary">
                            <i class="bi bi-plus-lg"></i>
                            Create First Exam
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .avatar-lg {
        width: 4rem;
        height: 4rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp5\htdocs\eduexamin\codes\resources\views/admin/subjects/show.blade.php ENDPATH**/ ?>