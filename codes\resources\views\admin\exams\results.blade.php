@extends('admin.layouts.app')

@section('title', 'Exam Results')
@section('page-title', 'Exam Results')

@section('content')
<div class="row">
    <div class="col-12">
        <!-- Exam Info Header -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    Results for: {{ $exam->title }}
                </h5>
                <p class="text-muted mb-0">Subject: {{ $exam->subject->name }}</p>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="h4 text-primary">{{ $attempts->count() }}</div>
                        <small class="text-muted">Total Attempts</small>
                    </div>
                    <div class="col-md-2">
                        <div class="h4 text-success">{{ $completedAttempts }}</div>
                        <small class="text-muted">Completed</small>
                    </div>
                    <div class="col-md-2">
                        <div class="h4 text-warning">{{ $attempts->count() - $completedAttempts }}</div>
                        <small class="text-muted">In Progress</small>
                    </div>
                    <div class="col-md-2">
                        <div class="h4 text-info">{{ number_format($averageScore, 1) }}%</div>
                        <small class="text-muted">Average Score</small>
                    </div>
                    <div class="col-md-2">
                        <div class="h4 text-success">{{ $highestScore }}%</div>
                        <small class="text-muted">Highest Score</small>
                    </div>
                    <div class="col-md-2">
                        <div class="h4 text-danger">{{ $lowestScore }}%</div>
                        <small class="text-muted">Lowest Score</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Table -->
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-table"></i>
                    Student Results
                </h6>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="exportResults()">
                        <i class="bi bi-download"></i>
                        Export CSV
                    </button>
                    <a href="{{ route('admin.exams.show', $exam) }}" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Exam
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if($attempts->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover" id="resultsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>Student</th>
                                    <th>Email</th>
                                    <th>Score</th>
                                    <th>Grade</th>
                                    <th>Started</th>
                                    <th>Submitted</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($attempts as $attempt)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="bi bi-person text-white"></i>
                                                </div>
                                                <div class="fw-bold">{{ $attempt->student->name }}</div>
                                            </div>
                                        </td>
                                        <td>
                                            <small>{{ $attempt->student->email }}</small>
                                        </td>
                                        <td>
                                            @if($attempt->score !== null)
                                                <span class="fw-bold text-{{ $attempt->score >= $exam->passing_score ? 'success' : 'danger' }}">
                                                    {{ $attempt->score }}%
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attempt->score !== null)
                                                @if($attempt->score >= $exam->passing_score)
                                                    <span class="badge bg-success">Pass</span>
                                                @else
                                                    <span class="badge bg-danger">Fail</span>
                                                @endif
                                            @else
                                                <span class="badge bg-secondary">N/A</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $attempt->started_at->format('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                <small>{{ $attempt->submitted_at->format('M d, Y H:i') }}</small>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                @php
                                                    $duration = $attempt->started_at->diffInMinutes($attempt->submitted_at);
                                                @endphp
                                                <small>{{ $duration }} min</small>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                <span class="badge bg-success">Completed</span>
                                            @else
                                                <span class="badge bg-warning">In Progress</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $attempts->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No attempts found for this exam.</p>
                        <a href="{{ route('admin.exams.show', $exam) }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i>
                            Back to Exam
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-sm {
        width: 1.5rem;
        height: 1.5rem;
    }
</style>
@endpush

@push('scripts')
<script>
function exportResults() {
    // Get table data
    const table = document.getElementById('resultsTable');
    const rows = table.querySelectorAll('tr');
    
    let csvContent = "data:text/csv;charset=utf-8,";
    
    // Add header row
    const headers = ['Student Name', 'Email', 'Score', 'Grade', 'Started', 'Submitted', 'Duration', 'Status'];
    csvContent += headers.join(',') + '\n';
    
    // Add data rows (skip header row)
    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const cells = row.querySelectorAll('td');
        
        const rowData = [
            cells[0].querySelector('.fw-bold').textContent.trim(),
            cells[1].textContent.trim(),
            cells[2].textContent.trim(),
            cells[3].textContent.trim(),
            cells[4].textContent.trim(),
            cells[5].textContent.trim(),
            cells[6].textContent.trim(),
            cells[7].textContent.trim()
        ];
        
        csvContent += rowData.map(field => `"${field}"`).join(',') + '\n';
    }
    
    // Create download link
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `{{ $exam->title }}_results_{{ now()->format('Y-m-d') }}.csv`);
    document.body.appendChild(link);
    
    // Trigger download
    link.click();
    document.body.removeChild(link);
}
</script>
@endpush
