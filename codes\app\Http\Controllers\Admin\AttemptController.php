<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Attempt;
use App\Models\Student;
use App\Models\Exam;
use App\Models\Subject;
use Illuminate\Http\Request;

class AttemptController extends Controller
{
    /**
     * Display a listing of attempts.
     */
    public function index(Request $request)
    {
        $query = Attempt::with(['student', 'exam.subject', 'subject']);

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'completed') {
                $query->whereNotNull('submitted_at');
            } elseif ($request->status === 'in_progress') {
                $query->whereNull('submitted_at');
            }
        }

        // Filter by student
        if ($request->filled('student_id')) {
            $query->where('student_id', $request->student_id);
        }

        // Filter by exam
        if ($request->filled('exam_id')) {
            $query->where('exam_id', $request->exam_id);
        }

        // Filter by subject
        if ($request->filled('subject_id')) {
            $query->where('subject_id', $request->subject_id);
        }

        // Search by student name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('student', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $attempts = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get filter options
        $students = Student::orderBy('name')->get(['id', 'name', 'email']);
        $exams = Exam::with('subject')->orderBy('title')->get(['id', 'title', 'subject_id']);
        $subjects = Subject::orderBy('name')->get(['id', 'name']);

        return view('admin.attempts.index', compact('attempts', 'students', 'exams', 'subjects'));
    }

    /**
     * Display the specified attempt.
     */
    public function show(Attempt $attempt)
    {
        $attempt->load([
            'student',
            'exam.subject',
            'subject',
            'attemptQuestions.attemptOptions',
            'attemptQuestions.question'
        ]);

        // Calculate detailed statistics
        $totalQuestions = $attempt->attemptQuestions->count();
        $answeredQuestions = $attempt->attemptQuestions->filter(function ($question) {
            return $question->attemptOptions->where('selected', true)->count() > 0;
        })->count();

        $correctAnswers = 0;
        $incorrectAnswers = 0;

        foreach ($attempt->attemptQuestions as $question) {
            $selectedOptions = $question->attemptOptions->where('selected', true);
            $correctOptions = $question->attemptOptions->where('is_correct', true);
            
            // Check if all correct options are selected and no incorrect options are selected
            $selectedCorrect = $selectedOptions->where('is_correct', true)->count();
            $selectedIncorrect = $selectedOptions->where('is_correct', false)->count();
            $totalCorrect = $correctOptions->count();
            
            if ($selectedCorrect === $totalCorrect && $selectedIncorrect === 0 && $selectedCorrect > 0) {
                $correctAnswers++;
            } elseif ($selectedOptions->count() > 0) {
                $incorrectAnswers++;
            }
        }

        $unansweredQuestions = $totalQuestions - $answeredQuestions;

        $statistics = [
            'total_questions' => $totalQuestions,
            'answered_questions' => $answeredQuestions,
            'unanswered_questions' => $unansweredQuestions,
            'correct_answers' => $correctAnswers,
            'incorrect_answers' => $incorrectAnswers,
            'accuracy' => $answeredQuestions > 0 ? round(($correctAnswers / $answeredQuestions) * 100, 2) : 0,
        ];

        return view('admin.attempts.show', compact('attempt', 'statistics'));
    }

    /**
     * Display attempts for a specific student.
     */
    public function studentAttempts(Student $student)
    {
        $attempts = $student->attempts()
            ->with(['exam.subject', 'subject'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        // Calculate student statistics
        $totalAttempts = $student->attempts->count();
        $completedAttempts = $student->attempts->whereNotNull('submitted_at')->count();
        $examAttempts = $student->attempts->where('type', 'exam')->count();
        $quizAttempts = $student->attempts->where('type', 'quiz')->count();
        
        $completedScores = $student->attempts
            ->whereNotNull('submitted_at')
            ->whereNotNull('score')
            ->pluck('score');
        
        $averageScore = $completedScores->count() > 0 ? $completedScores->average() : 0;
        $highestScore = $completedScores->count() > 0 ? $completedScores->max() : 0;

        $statistics = [
            'total_attempts' => $totalAttempts,
            'completed_attempts' => $completedAttempts,
            'exam_attempts' => $examAttempts,
            'quiz_attempts' => $quizAttempts,
            'average_score' => round($averageScore, 1),
            'highest_score' => $highestScore,
        ];

        return view('admin.attempts.student', compact('student', 'attempts', 'statistics'));
    }

    /**
     * Display attempts for a specific exam.
     */
    public function examAttempts(Exam $exam)
    {
        $attempts = $exam->attempts()
            ->with('student')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        // Calculate exam statistics
        $totalAttempts = $exam->attempts->count();
        $completedAttempts = $exam->attempts->whereNotNull('submitted_at')->count();
        
        $completedScores = $exam->attempts
            ->whereNotNull('submitted_at')
            ->whereNotNull('score')
            ->pluck('score');
        
        $averageScore = $completedScores->count() > 0 ? $completedScores->average() : 0;
        $highestScore = $completedScores->count() > 0 ? $completedScores->max() : 0;
        $lowestScore = $completedScores->count() > 0 ? $completedScores->min() : 0;
        
        $passedAttempts = $completedScores->filter(function ($score) use ($exam) {
            return $score >= ($exam->passing_score ?? 60);
        })->count();

        $statistics = [
            'total_attempts' => $totalAttempts,
            'completed_attempts' => $completedAttempts,
            'in_progress_attempts' => $totalAttempts - $completedAttempts,
            'average_score' => round($averageScore, 1),
            'highest_score' => $highestScore,
            'lowest_score' => $lowestScore,
            'passed_attempts' => $passedAttempts,
            'pass_rate' => $completedAttempts > 0 ? round(($passedAttempts / $completedAttempts) * 100, 1) : 0,
        ];

        return view('admin.attempts.exam', compact('exam', 'attempts', 'statistics'));
    }

    /**
     * Delete an attempt.
     */
    public function destroy(Attempt $attempt)
    {
        $attempt->delete();

        return redirect()->route('admin.attempts.index')
            ->with('success', 'Attempt deleted successfully.');
    }
}
