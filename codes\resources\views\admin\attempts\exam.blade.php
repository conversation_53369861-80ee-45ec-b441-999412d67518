@extends('admin.layouts.app')

@section('title', 'Exam Attempts')
@section('page-title', 'Exam Attempts')

@section('content')
<div class="row">
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clipboard-check"></i>
                    Exam Information
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                        <i class="bi bi-clipboard-check text-white" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="card-title">{{ $exam->title }}</h5>
                    @if($exam->description)
                        <p class="text-muted">{{ $exam->description }}</p>
                    @endif
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Subject</label>
                    <div>
                        <span class="badge bg-info fs-6">{{ $exam->subject->name }}</span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-6">
                        <label class="form-label text-muted">Duration</label>
                        <div>
                            <span class="badge bg-warning fs-6">{{ $exam->duration }} min</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <label class="form-label text-muted">Passing Score</label>
                        <div>
                            <span class="badge bg-success fs-6">{{ $exam->passing_score ?? 60 }}%</span>
                        </div>
                    </div>
                </div>

                <div class="row text-center mb-3">
                    <div class="col-4">
                        <div class="h4 text-primary">{{ $statistics['total_attempts'] }}</div>
                        <small class="text-muted">Total</small>
                    </div>
                    <div class="col-4">
                        <div class="h4 text-success">{{ $statistics['completed_attempts'] }}</div>
                        <small class="text-muted">Completed</small>
                    </div>
                    <div class="col-4">
                        <div class="h4 text-warning">{{ $statistics['in_progress_attempts'] }}</div>
                        <small class="text-muted">In Progress</small>
                    </div>
                </div>

                @if($statistics['completed_attempts'] > 0)
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <div class="h5 text-info">{{ $statistics['average_score'] }}%</div>
                            <small class="text-muted">Average</small>
                        </div>
                        <div class="col-4">
                            <div class="h5 text-success">{{ $statistics['highest_score'] }}%</div>
                            <small class="text-muted">Highest</small>
                        </div>
                        <div class="col-4">
                            <div class="h5 text-danger">{{ $statistics['lowest_score'] }}%</div>
                            <small class="text-muted">Lowest</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Pass Rate</label>
                        <div class="progress">
                            <div class="progress-bar bg-{{ $statistics['pass_rate'] >= 70 ? 'success' : ($statistics['pass_rate'] >= 50 ? 'warning' : 'danger') }}" 
                                 role="progressbar" 
                                 style="width: {{ $statistics['pass_rate'] }}%">
                                {{ $statistics['pass_rate'] }}%
                            </div>
                        </div>
                        <small class="text-muted">{{ $statistics['passed_attempts'] }} out of {{ $statistics['completed_attempts'] }} passed</small>
                    </div>
                @endif

                <hr>

                <div class="text-muted">
                    <small>
                        <i class="bi bi-person"></i>
                        Created by {{ $exam->admin->name }}
                    </small><br>
                    <small>
                        <i class="bi bi-calendar"></i>
                        {{ $exam->created_at->format('M d, Y') }}
                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.exams.index') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Exams
                    </a>
                    <a href="{{ route('admin.exams.show', $exam) }}" class="btn btn-primary">
                        <i class="bi bi-eye"></i>
                        View Exam
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-people"></i>
                    Student Attempts ({{ $attempts->total() }})
                </h6>
            </div>
            <div class="card-body">
                @if($attempts->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Student</th>
                                    <th>Score</th>
                                    <th>Grade</th>
                                    <th>Started</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($attempts as $attempt)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="bi bi-person text-white"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $attempt->student->name }}</div>
                                                    <small class="text-muted">{{ $attempt->student->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($attempt->score !== null)
                                                <span class="fw-bold text-{{ $attempt->score >= ($exam->passing_score ?? 60) ? 'success' : 'danger' }}">
                                                    {{ $attempt->score }}%
                                                </span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attempt->score !== null)
                                                @if($attempt->score >= ($exam->passing_score ?? 60))
                                                    <span class="badge bg-success">Pass</span>
                                                @else
                                                    <span class="badge bg-danger">Fail</span>
                                                @endif
                                            @else
                                                <span class="badge bg-secondary">N/A</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $attempt->started_at->format('M d, Y H:i') }}</small>
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                @php
                                                    $duration = $attempt->started_at->diffInMinutes($attempt->submitted_at);
                                                @endphp
                                                <small>{{ $duration }} min</small>
                                            @else
                                                @php
                                                    $duration = $attempt->started_at->diffInMinutes(now());
                                                @endphp
                                                <small class="text-warning">{{ $duration }} min (ongoing)</small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attempt->submitted_at)
                                                <span class="badge bg-success">Completed</span>
                                            @else
                                                <span class="badge bg-warning">In Progress</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.attempts.show', $attempt) }}" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.students.attempts', $attempt->student) }}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="Student Attempts">
                                                    <i class="bi bi-person"></i>
                                                </a>
                                                <form method="POST" 
                                                      action="{{ route('admin.attempts.destroy', $attempt) }}" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this attempt?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        {{ $attempts->links() }}
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No attempts found for this exam.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .avatar-lg {
        width: 4rem;
        height: 4rem;
    }
    .avatar-sm {
        width: 1.5rem;
        height: 1.5rem;
    }
</style>
@endpush
