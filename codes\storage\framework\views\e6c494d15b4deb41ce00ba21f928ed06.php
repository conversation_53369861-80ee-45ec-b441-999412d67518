<?php $__env->startSection('title', 'Question Details'); ?>
<?php $__env->startSection('page-title', 'Question Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    Question Information
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label text-muted">Subject</label>
                    <div>
                        <span class="badge bg-info fs-6"><?php echo e($question->subject->name); ?></span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Type</label>
                    <div>
                        <span class="badge bg-<?php echo e($question->type === 'mcq' ? 'primary' : 'success'); ?> fs-6">
                            <?php echo e($question->type === 'mcq' ? 'Multiple Choice' : 'True/False'); ?>

                        </span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Text Direction</label>
                    <div>
                        <span class="badge bg-secondary fs-6"><?php echo e(strtoupper($question->direction)); ?></span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Created By</label>
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                            <i class="bi bi-person text-white"></i>
                        </div>
                        <div>
                            <div class="fw-bold"><?php echo e($question->admin->name); ?></div>
                            <small class="text-muted"><?php echo e($question->admin->email); ?></small>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label text-muted">Statistics</label>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="h5 text-primary"><?php echo e($question->options->count()); ?></div>
                            <small class="text-muted">Options</small>
                        </div>
                        <div class="col-6">
                            <div class="h5 text-success"><?php echo e($question->exams->count()); ?></div>
                            <small class="text-muted">Used in Exams</small>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="text-muted">
                    <small>
                        <i class="bi bi-calendar"></i>
                        Created <?php echo e($question->created_at->format('M d, Y H:i')); ?>

                    </small>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="<?php echo e(route('admin.questions.index')); ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to List
                    </a>
                    <a href="<?php echo e(route('admin.questions.edit', $question)); ?>" class="btn btn-primary">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Question Text -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-question-circle-fill"></i>
                    Question Text
                </h5>
            </div>
            <div class="card-body">
                <div class="p-3 bg-light rounded" dir="<?php echo e($question->direction); ?>">
                    <p class="mb-0 fs-5"><?php echo e($question->question_text); ?></p>
                </div>
            </div>
        </div>

        <!-- Answer Options -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-check"></i>
                    Answer Options (<?php echo e($question->options->count()); ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if($question->options->count() > 0): ?>
                    <div class="row">
                        <?php $__currentLoopData = $question->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6 mb-3">
                                <div class="card <?php echo e($option->is_correct ? 'border-success' : 'border-light'); ?>">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <span class="badge bg-secondary">Option <?php echo e($index + 1); ?></span>
                                            <?php if($option->is_correct): ?>
                                                <span class="badge bg-success">
                                                    <i class="bi bi-check-lg"></i> Correct
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <div dir="<?php echo e($option->direction); ?>">
                                            <p class="mb-0"><?php echo e($option->option_text); ?></p>
                                        </div>
                                        <?php if($option->direction === 'rtl'): ?>
                                            <small class="text-muted">RTL</small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="bi bi-list-check text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No options found.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Used in Exams -->
        <?php if($question->exams->count() > 0): ?>
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clipboard-check"></i>
                        Used in Exams (<?php echo e($question->exams->count()); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Exam Title</th>
                                    <th>Subject</th>
                                    <th>Duration</th>
                                    <th>Start Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $question->exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?php echo e($exam->title); ?></div>
                                            <?php if($exam->description): ?>
                                                <small class="text-muted"><?php echo e(Str::limit($exam->description, 50)); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($exam->subject->name); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo e($exam->duration); ?> min</span>
                                        </td>
                                        <td>
                                            <small><?php echo e($exam->start_time->format('M d, Y H:i')); ?></small>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(route('admin.exams.show', $exam)); ?>" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="View Exam">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .avatar-sm {
        width: 2rem;
        height: 2rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp5\htdocs\eduexamin\codes\resources\views/admin/questions/show.blade.php ENDPATH**/ ?>