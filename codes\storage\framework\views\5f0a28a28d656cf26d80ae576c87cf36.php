<?php $__env->startSection('title', 'Admins'); ?>
<?php $__env->startSection('page-title', 'Admin Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-people-fill"></i>
                    All Admins
                </h6>
                <a href="<?php echo e(route('admin.admins.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-lg"></i>
                    Add New Admin
                </a>
            </div>
            <div class="card-body">
                <?php if($admins->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Questions Created</th>
                                    <th>Exams Created</th>
                                    <th>Created At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $admins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $admin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($admin->id); ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="bi bi-person text-white"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold"><?php echo e($admin->name); ?></div>
                                                    <?php if($admin->id === auth('admin')->id()): ?>
                                                        <small class="text-primary">(You)</small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo e($admin->email); ?></td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($admin->questions_count ?? 0); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo e($admin->exams_count ?? 0); ?></span>
                                        </td>
                                        <td>
                                            <small><?php echo e($admin->created_at->format('M d, Y')); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('admin.admins.show', $admin)); ?>" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.admins.edit', $admin)); ?>" 
                                                   class="btn btn-sm btn-outline-warning" 
                                                   title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <?php if($admin->id !== auth('admin')->id()): ?>
                                                    <form method="POST" 
                                                          action="<?php echo e(route('admin.admins.destroy', $admin)); ?>" 
                                                          class="d-inline"
                                                          onsubmit="return confirm('Are you sure you want to delete this admin?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" 
                                                                class="btn btn-sm btn-outline-danger" 
                                                                title="Delete">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <?php echo e($admins->links()); ?>

                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">No admins found.</p>
                        <a href="<?php echo e(route('admin.admins.create')); ?>" class="btn btn-primary">
                            <i class="bi bi-plus-lg"></i>
                            Add First Admin
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .avatar-sm {
        width: 2rem;
        height: 2rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp5\htdocs\eduexamin\codes\resources\views/admin/admins/index.blade.php ENDPATH**/ ?>