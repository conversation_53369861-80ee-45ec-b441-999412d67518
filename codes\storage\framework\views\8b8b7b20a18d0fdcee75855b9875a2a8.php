<?php $__env->startSection('title', 'Student Details'); ?>
<?php $__env->startSection('page-title', 'Student Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-circle"></i>
                    Student Information
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="avatar-lg bg-info rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                    <i class="bi bi-person text-white" style="font-size: 2rem;"></i>
                </div>
                <h5 class="card-title"><?php echo e($student->name); ?></h5>
                <p class="text-muted"><?php echo e($student->email); ?></p>
                
                <?php if($student->email_verified_at): ?>
                    <span class="badge bg-success mb-3">Email Verified</span>
                <?php else: ?>
                    <span class="badge bg-warning mb-3">Email Not Verified</span>
                <?php endif; ?>

                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 text-primary"><?php echo e($student->attempts->count()); ?></div>
                        <small class="text-muted">Total Attempts</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success"><?php echo e($student->savedExams->count()); ?></div>
                        <small class="text-muted">Saved Exams</small>
                    </div>
                </div>

                <hr>

                <div class="text-muted">
                    <small>
                        <i class="bi bi-calendar"></i>
                        Joined <?php echo e($student->created_at->format('M d, Y')); ?>

                    </small>
                </div>
                
                <?php if($student->email_verified_at): ?>
                    <div class="text-muted mt-1">
                        <small>
                            <i class="bi bi-check-circle"></i>
                            Verified <?php echo e($student->email_verified_at->format('M d, Y')); ?>

                        </small>
                    </div>
                <?php endif; ?>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="<?php echo e(route('admin.students.index')); ?>" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to List
                    </a>
                    <a href="<?php echo e(route('admin.students.edit', $student)); ?>" class="btn btn-primary">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Exam Attempts -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clipboard-check"></i>
                    Exam Attempts (<?php echo e($student->attempts->where('type', 'exam')->count()); ?>)
                </h6>
            </div>
            <div class="card-body">
                <?php $examAttempts = $student->attempts->where('type', 'exam') ?>
                <?php if($examAttempts->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Exam</th>
                                    <th>Score</th>
                                    <th>Started</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $examAttempts->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attempt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?php echo e($attempt->exam->title); ?></div>
                                            <small class="text-muted"><?php echo e($attempt->exam->subject->name); ?></small>
                                        </td>
                                        <td>
                                            <?php if($attempt->score !== null): ?>
                                                <span class="fw-bold text-success"><?php echo e($attempt->score); ?>%</span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo e($attempt->started_at->format('M d, Y H:i')); ?></small>
                                        </td>
                                        <td>
                                            <?php if($attempt->submitted_at): ?>
                                                <span class="badge bg-success">Completed</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">In Progress</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="bi bi-clipboard-check text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No exam attempts yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quiz Attempts -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-question-circle"></i>
                    Quiz Attempts (<?php echo e($student->attempts->where('type', 'quiz')->count()); ?>)
                </h6>
            </div>
            <div class="card-body">
                <?php $quizAttempts = $student->attempts->where('type', 'quiz') ?>
                <?php if($quizAttempts->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Subject</th>
                                    <th>Score</th>
                                    <th>Started</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $quizAttempts->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attempt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?php echo e($attempt->subject->name); ?></div>
                                            <small class="text-muted">Quiz</small>
                                        </td>
                                        <td>
                                            <?php if($attempt->score !== null): ?>
                                                <span class="fw-bold text-success"><?php echo e($attempt->score); ?>%</span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small><?php echo e($attempt->started_at->format('M d, Y H:i')); ?></small>
                                        </td>
                                        <td>
                                            <?php if($attempt->submitted_at): ?>
                                                <span class="badge bg-success">Completed</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">In Progress</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    <a class="btn btn-primary" href="<?php echo e(route('admin.students.attempts', $student)); ?>">All attemps</a>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="bi bi-question-circle text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No quiz attempts yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Saved Exams -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-bookmark"></i>
                    Saved Exams (<?php echo e($student->savedExams->count()); ?>)
                </h6>
            </div>
            <div class="card-body">
                <?php if($student->savedExams->count() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Exam</th>
                                    <th>Subject</th>
                                    <th>Duration</th>
                                    <th>Saved At</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $student->savedExams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?php echo e($exam->title); ?></div>
                                            <?php if($exam->description): ?>
                                                <small class="text-muted"><?php echo e(Str::limit($exam->description, 50)); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo e($exam->subject->name); ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning"><?php echo e($exam->duration); ?> min</span>
                                        </td>
                                        <td>
                                            <small><?php echo e($exam->pivot->created_at->format('M d, Y')); ?></small>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="bi bi-bookmark text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No saved exams yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .avatar-lg {
        width: 4rem;
        height: 4rem;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp5\htdocs\eduexamin\codes\resources\views/admin/students/show.blade.php ENDPATH**/ ?>