<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Create roles
        $adminRole = Role::create(['name' => 'admin']);
        $studentRole = Role::create(['name' => 'student']);

        // Create permissions
        $permissions = [
            'manage-admins',
            'manage-students',
            'manage-subjects',
            'manage-questions',
            'manage-exams',
            'view-analytics',
            'take-exams',
            'take-quizzes',
            'view-results',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Assign permissions to admin
        $adminRole->givePermissionTo([
            'manage-admins',
            'manage-students',
            'manage-subjects',
            'manage-questions',
            'manage-exams',
            'view-analytics',
        ]);

        // Assign permissions to student
        $studentRole->givePermissionTo([
            'take-exams',
            'take-quizzes',
            'view-results',
        ]);
    }
}