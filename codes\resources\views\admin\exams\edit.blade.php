@extends('admin.layouts.app')

@section('title', 'Edit Exam')
@section('page-title', 'Edit Exam')

@section('content')
<div class="row justify-content-center">
    <div class="col-md-10 col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pencil"></i>
                    Edit Exam: {{ $exam->title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.exams.update', $exam) }}">
                    @csrf
                    @method('PUT')

                    <div class="mb-3">
                        <label for="subject_id" class="form-label">Subject</label>
                        <select class="form-select @error('subject_id') is-invalid @enderror" 
                                id="subject_id" 
                                name="subject_id" 
                                required>
                            <option value="">Select Subject</option>
                            @foreach($subjects as $subject)
                                <option value="{{ $subject->id }}" 
                                        {{ old('subject_id', $exam->subject_id) == $subject->id ? 'selected' : '' }}>
                                    {{ $subject->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('subject_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">Exam Title</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-clipboard-check"></i>
                            </span>
                            <input type="text" 
                                   class="form-control @error('title') is-invalid @enderror" 
                                   id="title" 
                                   name="title" 
                                   value="{{ old('title', $exam->title) }}" 
                                   required 
                                   autofocus
                                   placeholder="Enter exam title">
                        </div>
                        @error('title')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description (Optional)</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="3"
                                  placeholder="Enter exam description">{{ old('description', $exam->description) }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="duration" class="form-label">Duration (minutes)</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-clock"></i>
                                    </span>
                                    <input type="number" 
                                           class="form-control @error('duration') is-invalid @enderror" 
                                           id="duration" 
                                           name="duration" 
                                           value="{{ old('duration', $exam->duration) }}" 
                                           min="1" 
                                           max="300" 
                                           required
                                           placeholder="60">
                                    <span class="input-group-text">min</span>
                                </div>
                                @error('duration')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_attempts" class="form-label">Max Attempts</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-arrow-repeat"></i>
                                    </span>
                                    <input type="number" 
                                           class="form-control @error('max_attempts') is-invalid @enderror" 
                                           id="max_attempts" 
                                           name="max_attempts" 
                                           value="{{ old('max_attempts', $exam->max_attempts) }}" 
                                           min="1" 
                                           max="10" 
                                           required
                                           placeholder="1">
                                </div>
                                @error('max_attempts')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_time" class="form-label">Start Time</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-calendar-event"></i>
                                    </span>
                                    <input type="datetime-local" 
                                           class="form-control @error('start_time') is-invalid @enderror" 
                                           id="start_time" 
                                           name="start_time" 
                                           value="{{ old('start_time', $exam->start_time->format('Y-m-d\TH:i')) }}" 
                                           required>
                                </div>
                                @error('start_time')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_time" class="form-label">End Time</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-calendar-x"></i>
                                    </span>
                                    <input type="datetime-local" 
                                           class="form-control @error('end_time') is-invalid @enderror" 
                                           id="end_time" 
                                           name="end_time" 
                                           value="{{ old('end_time', $exam->end_time->format('Y-m-d\TH:i')) }}" 
                                           required>
                                </div>
                                @error('end_time')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="passing_score" class="form-label">Passing Score (%)</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-percent"></i>
                                    </span>
                                    <input type="number" 
                                           class="form-control @error('passing_score') is-invalid @enderror" 
                                           id="passing_score" 
                                           name="passing_score" 
                                           value="{{ old('passing_score', $exam->passing_score) }}" 
                                           min="0" 
                                           max="100" 
                                           required
                                           placeholder="60">
                                    <span class="input-group-text">%</span>
                                </div>
                                @error('passing_score')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Options</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="shuffle_questions" 
                                           name="shuffle_questions" 
                                           value="1"
                                           {{ old('shuffle_questions', $exam->shuffle_questions) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="shuffle_questions">
                                        Shuffle Questions
                                    </label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="show_results" 
                                           name="show_results" 
                                           value="1"
                                           {{ old('show_results', $exam->show_results) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="show_results">
                                        Show Results to Students
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.exams.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i>
                            Back to List
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i>
                            Update Exam
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validate end time is after start time
    document.getElementById('end_time').addEventListener('change', function() {
        const startTime = new Date(document.getElementById('start_time').value);
        const endTime = new Date(this.value);
        
        if (startTime && endTime && endTime <= startTime) {
            alert('End time must be after start time');
            this.focus();
        }
    });
});
</script>
@endpush
