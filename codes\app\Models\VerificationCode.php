<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class VerificationCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'code',
        'expires_at',
    ];

    protected $dates = [
        'expires_at',
    ];

    /**
     * Relationship: each code belongs to one user.
     */
    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Check if the code is expired.
     */
    public function isExpired(): bool
    {
        return Carbon::now()->greaterThan($this->expires_at);
    }
}
